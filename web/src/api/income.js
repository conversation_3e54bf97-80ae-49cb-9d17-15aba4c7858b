import request from '@/libs/request';

/**
 * @description 获取用户收益统计信息
 * @param {Object} params {userId}
 */
export function getUserIncomeStatistics(params) {
  return request({
    url: '/finance/income/statistics',
    method: 'get',
    params
  });
}

/**
 * @description 获取用户收益明细列表
 * @param {Object} params {userId, page, limit, incomeType, startDate, endDate}
 */
export function getUserIncomeDetails(params) {
  return request({
    url: '/finance/income/details',
    method: 'post',
    params
  });
}

/**
 * @description 导出用户收益明细
 * @param {Object} params {userId, incomeType, startDate, endDate}
 */
export function exportUserIncomeDetails(params) {
  return request({
    url: '/finance/income/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}