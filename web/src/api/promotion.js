import request from '@/libs/request';

/**
 * @description 获取推广用户详情
 * @param {Object} data {userId, searchKeyword, page, size}
 */
export function getPromotionUserDetail(data) {
  return request({
    url: '/promotion/user/detail',
    method: 'post',
    data
  });
}

/**
 * @description 获取推广用户列表
 * @param {Object} params 查询参数
 */
export function getPromotionUserList(params) {
  return request({
    url: '/promotion/user/list',
    method: 'get',
    params
  });
}

/**
 * @description 搜索推广用户
 * @param {Object} params {searchKeyword, page, size}
 */
export function searchPromotionUsers(params) {
  return request({
    url: '/promotion/user/search',
    method: 'get',
    params
  });
}