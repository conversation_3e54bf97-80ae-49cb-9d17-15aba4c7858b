<template>
  <div class="income-details-container">
    <el-card :bordered="false" shadow="never">
      <div slot="header" class="clearfix">
        <span class="header-title">{{ userName }}的收益明细</span>
        <el-button style="float: right;" type="primary" @click="goBack">返回</el-button>
      </div>
      
      <!-- 筛选条件 -->
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" ref="searchForm">
        <el-form-item label="时间范围：">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            @change="onDateChange"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="收益类型：">
          <el-select v-model="searchForm.incomeType" placeholder="请选择收益类型" clearable>
            <el-option
              v-for="option in incomeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 统计信息 -->
      <el-row :gutter="20" class="statistics-row">
        <el-col :span="6">
          <div class="statistic-card">
            <div class="statistic-title">总收益</div>
            <div class="statistic-value">¥{{ statistics.totalIncome || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="statistic-card">
            <div class="statistic-title">已确认</div>
            <div class="statistic-value">¥{{ statistics.confirmedAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="statistic-card">
            <div class="statistic-title">待确认</div>
            <div class="statistic-value">¥{{ statistics.pendingAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="statistic-card">
            <div class="statistic-title">已结算</div>
            <div class="statistic-value">¥{{ statistics.settledAmount || '0.00' }}</div>
          </div>
        </el-col>
      </el-row>

      <!-- 收益类型统计 -->
      <el-row :gutter="20" class="statistics-row">
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">分销佣金</div>
            <div class="statistic-value small">¥{{ statistics.commissionAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">阶梯推广</div>
            <div class="statistic-value small">¥{{ statistics.tieredPromotionAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">总监返现</div>
            <div class="statistic-value small">¥{{ statistics.directorAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">团队奖励</div>
            <div class="statistic-value small">¥{{ statistics.teamAmount || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">本月收益</div>
            <div class="statistic-value small">¥{{ statistics.monthlyIncome || '0.00' }}</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="statistic-card small">
            <div class="statistic-title">今日收益</div>
            <div class="statistic-value small">¥{{ statistics.dailyIncome || '0.00' }}</div>
          </div>
        </el-col>
      </el-row>
      
      <!-- 收益明细列表 -->
      <el-table
        :data="incomeList"
        v-loading="loading"
        style="width: 100%; margin-top: 20px;"
        border
      >
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="incomeTypeText" label="收益类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getIncomeTypeTagType(scope.row.incomeType)">
              {{ scope.row.incomeTypeText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderSn" label="关联订单号" width="180"></el-table-column>
        <el-table-column prop="amount" label="收益金额" width="120">
          <template slot-scope="scope">
            <span class="income-amount">+¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="statusText" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="收益说明" min-width="200"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
        <el-table-column prop="confirmTime" label="确认时间" width="180"></el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { formatDate } from '@/utils/validate';
import timeOptions from '@/libs/timeOptions';
import { getUserIncomeStatistics, getUserIncomeDetails } from '@/api/income';

export default {
  name: 'incomeDetails',
  data() {
    return {
      userId: null,
      userName: '',
      dateRange: [],
      pickerOptions: timeOptions,
      searchForm: {
        incomeType: '',
        status: ''
      },
      incomeTypeOptions: [
        { label: '全部', value: '' },
        { label: '分销佣金', value: 1 },
        { label: '阶梯推广', value: 2 },
        { label: '总监返现', value: 3 },
        { label: '团队奖励', value: 4 },
        { label: '其他收益', value: 5 }
      ],
      statusOptions: [
        { label: '全部', value: '' },
        { label: '待确认', value: 0 },
        { label: '已确认', value: 1 },
        { label: '已结算', value: 2 },
        { label: '已冻结', value: 3 },
        { label: '已取消', value: 4 }
      ],
      statistics: {
        totalIncome: '0.00',
        confirmedAmount: '0.00',
        pendingAmount: '0.00',
        settledAmount: '0.00',
        frozenAmount: '0.00',
        commissionAmount: '0.00',
        tieredPromotionAmount: '0.00',
        directorAmount: '0.00',
        teamAmount: '0.00',
        otherAmount: '0.00',
        monthlyIncome: '0.00',
        dailyIncome: '0.00'
      },
      incomeList: [],
      loading: false,
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      }
    };
  },
  created() {
    // 获取路由参数
    this.userId = this.$route.query.userId;
    this.userName = this.$route.query.userName || '用户';
    
    // 初始化时间范围为最近30天
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000);
    this.dateRange = [formatDate(start, 'yyyy-MM-dd'), formatDate(end, 'yyyy-MM-dd')];
    
    // 加载数据
    this.loadStatistics();
    this.loadIncomeList();
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 日期改变事件
    onDateChange(value) {
      this.handleSearch();
    },
    
    // 查询
    handleSearch() {
      this.pagination.page = 1;
      this.loadIncomeList();
    },
    
    // 重置
    handleReset() {
      this.dateRange = [];
      this.searchForm.incomeType = '';
      this.searchForm.status = '';
      this.pagination.page = 1;
      this.loadIncomeList();
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.limit = val;
      this.pagination.page = 1;
      this.loadIncomeList();
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.loadIncomeList();
    },
    
    // 获取收益类型标签类型
    getIncomeTypeTagType(type) {
      const typeMap = {
        'commission': 'success',
        'invitation_bonus': 'primary',
        'team_bonus': 'warning',
        'other': 'info'
      };
      return typeMap[type] || 'info';
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'pending': 'warning',    // 待确认
        'confirmed': 'success',  // 已确认
        'withdrawn': 'info',      // 已提现
        'cancelled': 'danger'    // 已取消
      };
      return statusMap[status] || 'info';
    },
    
    // 加载统计信息
    loadStatistics() {
      if (!this.userId) return;
      
      getUserIncomeStatistics({ userId: this.userId })
        .then(res => {
          if (res.success) {
            this.statistics = res.data || {};
          }
        })
        .catch(err => {
          this.$message.error('获取统计信息失败: ' + err.msg);
        });
    },
    
    // 加载收益明细列表
    loadIncomeList() {
      if (!this.userId) return;
      
      this.loading = true;
      
      // 构造查询参数
      const params = {
        userId: this.userId,
        page: this.pagination.page,
        limit: this.pagination.limit,
        incomeType: this.searchForm.incomeType,
        status: this.searchForm.status,
        startDate: this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : null,
        endDate: this.dateRange && this.dateRange.length > 1 ? this.dateRange[1] : null
      };
      
      getUserIncomeDetails(params)
        .then(res => {
          if (res.success) {
            this.incomeList = res.data.list || [];
            this.pagination.total = res.data.total || 0;
          }
        })
        .catch(err => {
          this.$message.error('获取收益明细失败: ' + err.msg);
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.income-details-container {
  padding: 20px;
  
  .header-title {
    font-size: 18px;
    font-weight: bold;
  }
  
  .statistics-row {
    margin-top: 20px;
    
    .statistic-card {
      background-color: #f5f7fa;
      border-radius: 4px;
      padding: 15px;
      text-align: center;

      &.small {
        padding: 12px;
      }

      .statistic-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }

      .statistic-value {
        font-size: 20px;
        font-weight: bold;
        color: #303133;

        &.small {
          font-size: 16px;
        }
      }
    }
  }
  
  .income-amount {
    color: #67c23a;
    font-weight: bold;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>