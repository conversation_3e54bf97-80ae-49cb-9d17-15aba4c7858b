<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mb-16" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="formValidate"
          :model="formValidate"
          :label-width="labelWidth"
          label-position="right"
          inline
          @submit.native.prevent
        >
          <el-form-item label="时间范围：">
            <el-date-picker
              clearable
              v-model="timeVal"
              type="daterange"
              :editable="false"
              @change="onchangeTime"
              format="yyyy/MM/dd"
              value-format="yyyy/MM/dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              style="width: 250px"
              class="mr20"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="交易类型：">
            <el-select 
              clearable 
              v-model="formValidate.trading_type" 
              @change="selChange" 
              class="form_content_width"
              placeholder="请选择交易类型"
            >
              <el-option
                :label="item"
                :value="Object.keys(typeOptions)[index]"
                v-for="(item, index) in Object.values(typeOptions)"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键词：">
            <el-input
              v-model="formValidate.keywords"
              placeholder="请输入用户昵称、备注等关键词"
              @keyup.enter.native="searchData"
              clearable
              style="width: 200px"
            >
              <el-button slot="append" icon="el-icon-search" @click="searchData"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据统计卡片 -->
    <el-row :gutter="16" class="ivu-mb-16">
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-title">今日余额变动</div>
            <div class="statistic-value">¥{{ todayBalance }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-title">今日余额充值</div>
            <div class="statistic-value recharge-color">¥{{ todayRecharge }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-title">今日余额使用</div>
            <div class="statistic-value use-color">¥{{ todayUse }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-title">余额记录总数</div>
            <div class="statistic-value">{{ total }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card :bordered="false" shadow="never">
      <div class="table-header">
        <h3>余额交易记录</h3>
        <div class="table-actions">
          <el-button 
            type="success" 
            size="small" 
            @click="exportData"
            :loading="exportLoading"
            icon="el-icon-download"
          >
            导出数据
          </el-button>
          <el-button 
            size="small" 
            @click="refreshData"
            icon="el-icon-refresh"
          >
            刷新
          </el-button>
        </div>
      </div>
      <el-table 
        ref="table" 
        :data="tabList" 
        v-loading="loading" 
        empty-text="暂无数据"
        stripe
        border
      >
        <el-table-column label="ID" width="70" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="用户信息" min-width="120">
          <template slot-scope="scope">
            <div class="user-info">
              <div class="nickname">{{ scope.row.nickname }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="关联信息" min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.relation">
              <!-- 可跳转的关联信息（订单） -->
              <el-tag 
                v-if="scope.row.canJump"
                :type="getRelationTagType(scope.row.relation)"
                size="small"
                class="relation-link"
                @click="handleRelationClick(scope.row)"
              >
                <i class="el-icon-view"></i>
                {{ scope.row.relation }}
              </el-tag>
              <!-- 不可跳转的关联信息 -->
              <el-tag 
                v-else
                :type="getRelationTagType(scope.row.relation)"
                size="small"
              >
                {{ scope.row.relation }}
              </el-tag>
            </div>
            <span v-else class="no-relation">无</span>
          </template>
        </el-table-column>
        
        <el-table-column label="交易时间" min-width="140" sortable>
          <template slot-scope="scope">
            <i class="el-icon-time"></i>
            <span>{{ scope.row.add_time }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="交易金额" min-width="100" align="center" sortable>
          <template slot-scope="scope">
            <div v-if="scope.row.pm" class="z-price">
              <i class="el-icon-top"></i>
              + ¥{{ scope.row.number }}
            </div>
            <div v-else class="f-price">
              <i class="el-icon-bottom"></i>
              - ¥{{ scope.row.number }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="交易类型" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag 
              :type="getTypeTagColor(scope.row.type_name)"
              size="small"
            >
              {{ scope.row.type_name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="备注" min-width="150">
          <template slot-scope="scope">
            <div class="remark-cell">
              <span v-if="scope.row.mark" class="remark-text">{{ scope.row.mark }}</span>
              <span v-else class="no-remark">无备注</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" fixed="right" width="120" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="setMark(scope.row)"
              v-db-click
            >
              <i class="el-icon-edit"></i>
              备注
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="formValidate.page"
          :limit.sync="formValidate.limit"
          @pagination="getList"
        />
      </div>
    </el-card>
    <!-- 备注编辑对话框 -->
    <el-dialog 
      :visible.sync="modals" 
      title="编辑备注" 
      :close-on-click-modal="false" 
      width="540px"
      :before-close="handleClose"
    >
      <el-form :model="mark_msg" label-width="80px">
        <el-form-item label="备注内容:">
          <el-input 
            v-model="mark_msg.mark" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入备注内容，最多200字符" 
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button 
          type="primary" 
          @click="oks" 
          :loading="modal_loading"
          v-db-click
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
    
    <!-- 订单详情抽屉 -->
    <order-details 
      ref="orderDetails" 
      :orderDatalist="orderDatalist" 
      :orderId="currentOrderId"
    ></order-details>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { getBalanceList, setBalanceMark } from '@/api/finance';
import { getDataInfo } from '@/api/order'; // 导入获取订单详情的API
import { formatDate } from '@/utils/validate';
import dateRadio from '@/components/dateRadio';
import orderDetails from '@/pages/order/orderList/handle/orderDetails'; // 导入订单详情组件
export default {
  name: 'balance',
  components: { dateRadio, orderDetails },
  filters: {
    formatDate(time) {
      if (time !== 0) {
        let date = new Date(time * 1000);
        return formatDate(date, 'yyyy-MM-dd hh:mm');
      }
    },
  },
  data() {
    return {
      images: ['1.jpg', '2.jpg'],
      modal_loading: false,
      exportLoading: false,
      pickerOptions: this.$timeOptions,
      mark_msg: {
        mark: '',
      },
      modals: false,
      total: 0,
      loading: false,
      tabList: [],
      typeOptions: {}, // 交易类型选项
      selectIndexTime: '',
      
      // 统计数据
      todayBalance: '0.00',
      todayRecharge: '0.00',
      todayUse: '0.00',
      
      formValidate: {
        trading_type: '',
        time: '',
        keywords: '',
        page: 1,
        limit: 20,
      },
      timeVal: [],
      FromData: null,
      extractId: 0,
      
      // 订单详情相关
      orderDatalist: {}, // 订单详情数据
      currentOrderId: 0, // 当前查看的订单ID
    };
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '80px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 获取关联信息标签类型
    getRelationTagType(relation) {
      if (!relation) return 'info';
      if (relation.includes('订单')) return 'primary';
      if (relation.includes('充值')) return 'success';
      if (relation.includes('调整')) return 'warning';
      return 'info';
    },
    
    // 获取交易类型标签颜色
    getTypeTagColor(typeName) {
      switch (typeName) {
        case '余额充值':
          return 'success';
        case '余额使用':
          return 'danger';
        case '余额退回':
          return 'warning';
        case '手动调整':
          return 'info';
        default:
          return 'info';
      }
    },
    
    // 处理关联信息点击事件
    handleRelationClick(row) {
      if (!row.canJump || !row.relationSourceId) {
        return;
      }
      
      // 根据关联类型进行不同的跳转操作
      if (row.relationSource === 'order' || row.relationSource === 'order_cancel') {
        // 跳转到订单详情页面
        this.goToOrderDetail(row.relationSourceId);
      }
    },
    
    // 查看订单详情
    goToOrderDetail(orderId) {
      if (!orderId) {
        this.$message.error('订单ID不能为空');
        return;
      }
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载订单详情...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 获取订单详情数据
      getDataInfo(orderId)
        .then((res) => {
          loading.close();
          if (res.success) {
            this.currentOrderId = orderId;
            this.orderDatalist = res.data;
            // 打开订单详情抽屉
            this.$refs.orderDetails.modals = true;
            
            this.$message.success(`成功加载订单 #${orderId} 详情`);
          } else {
            this.$message.error(res.message || '获取订单详情失败');
          }
        })
        .catch((error) => {
          loading.close();
          console.error('获取订单详情失败:', error);
          this.$message.error('获取订单详情失败，请稍后重试');
        });
    },
    
    // 搜索数据
    searchData() {
      this.formValidate.page = 1;
      this.getList();
    },
    
    // 重置搜索
    resetSearch() {
      this.formValidate = {
        trading_type: '',
        time: '',
        keywords: '',
        page: 1,
        limit: 20,
      };
      this.timeVal = [];
      this.getList();
    },
    
    // 刷新数据
    refreshData() {
      this.getList();
      this.calculateTodayStatistics();
    },
    
    // 导出数据
    exportData() {
      this.exportLoading = true;
      // 模拟导出功能
      setTimeout(() => {
        this.$message.success('导出功能开发中...');
        this.exportLoading = false;
      }, 2000);
    },
    
    // 计算今日统计数据
    calculateTodayStatistics() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      let todayBalance = 0;
      let todayRecharge = 0;
      let todayUse = 0;
      
      this.tabList.forEach(item => {
        const itemDate = item.add_time.split(' ')[0];
        if (itemDate === todayStr) {
          const amount = parseFloat(item.number);
          if (item.pm) {
            todayBalance += amount;
            if (item.type_name === '余额充值') {
              todayRecharge += amount;
            }
          } else {
            todayBalance -= amount;
            if (item.type_name === '余额使用') {
              todayUse += amount;
            }
          }
        }
      });
      
      this.todayBalance = todayBalance.toFixed(2);
      this.todayRecharge = todayRecharge.toFixed(2);
      this.todayUse = todayUse.toFixed(2);
    },
    
    // 关闭对话框
    handleClose() {
      this.modals = false;
      this.mark_msg.mark = '';
    },
    // 确定设置备注
    oks() {
      if (!this.mark_msg.mark.trim() && !confirm('确定要清空备注吗？')) {
        return;
      }
      
      this.modal_loading = true;
      this.mark_msg.mark = this.mark_msg.mark.trim();
      
      setBalanceMark(this.extractId, this.mark_msg)
        .then(async (res) => {
          if (res.code === 0) {
            this.$message.success(res.message || '设置备注成功');
            this.modal_loading = false;
            this.modals = false;
            this.getList(); // 刷新列表
          } else {
            this.modal_loading = false;
            this.$message.error(res.message || '设置备注失败');
          }
        })
        .catch((res) => {
          this.modal_loading = false;
          this.$message.error(res.message || '设置备注失败');
        });
    },
    // 备注
    setMark(row) {
      this.modals = true;
      this.extractId = row.id;
      this.mark_msg.mark = row.mark;
    },
    onSelectDate(e) {
      this.formValidate.time = e;
      this.formValidate.page = 1;
      this.getList();
    },
    dateToMs(date) {
      let result = new Date(date).getTime();
      return result;
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.formValidate.time = this.timeVal ? this.timeVal.join('-') : '';
      this.formValidate.page = 1;
      this.getList();
    },
    // 选择交易类型
    selChange(e) {
      this.formValidate.page = 1;
      this.formValidate.trading_type = e;
      this.getList();
    },
    // 获取列表数据
    getList() {
      this.loading = true;
      getBalanceList(this.formValidate)
        .then(async (res) => {
          if (res.success) {
            let data = res.data;
            this.tabList = data.list || [];
            this.total = data.count || 0;
            this.typeOptions = data.status || {};
            
            // 计算今日统计
            this.calculateTodayStatistics();
          } else {
            this.$message.error(res.message || '获取数据失败');
          }
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.message || '网络请求失败');
        });
    },
    // 编辑提交成功
    submitFail() {
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
// 基本样式
.ivu-mt .type .item {
  margin: 3px 0;
}

.Refresh {
  font-size: 12px;
  color: var(--prev-color-primary);
  cursor: pointer;
}

.ivu-form-item {
  margin-bottom: 10px;
}

.status ::v-deep .item ~ .item {
  margin-left: 6px;
}

.status ::v-deep .statusVal {
  margin-bottom: 7px;
}

.type {
  padding: 3px 0;
  box-sizing: border-box;
}

// 统计卡片样式
.statistic-card {
  text-align: center;
  padding: 20px;
  
  .statistic-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }
  
  .statistic-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    
    &.recharge-color {
      color: #67c23a;
    }
    
    &.use-color {
      color: #f56c6c;
    }
  }
}

// 表格头部样式
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .table-actions {
    .el-button {
      margin-left: 8px;
    }
  }
}

// 用户信息样式
.user-info {
  .nickname {
    font-weight: 500;
    color: #333;
  }
}

// 金额显示样式
.z-price {
  color: #67c23a;
  font-weight: bold;
  
  i {
    margin-right: 4px;
  }
}

.f-price {
  color: #f56c6c;
  font-weight: bold;
  
  i {
    margin-right: 4px;
  }
}

// 备注单元格样式
.remark-cell {
  .remark-text {
    color: #333;
    word-break: break-all;
  }
  
  .no-remark {
    color: #999;
    font-style: italic;
  }
}

// 关联信息样式
.relation-link {
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  i {
    margin-right: 4px;
  }
}

.no-relation {
  color: #999;
  font-style: italic;
}

// 分页样式
.page {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

// 响应式样式
@media (max-width: 768px) {
  .statistic-card {
    padding: 15px;
    
    .statistic-value {
      font-size: 20px;
    }
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    .table-actions {
      align-self: stretch;
      display: flex;
      justify-content: flex-end;
    }
  }
}

// 其他原有样式
.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
