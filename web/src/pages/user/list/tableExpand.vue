<template>
  <div>
    <el-row class="expand-row">
      <el-col :span="6">
        <span class="expand-key">用户ID：</span>
        <span class="expand-value">{{ row.uid }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">真实姓名：</span>
        <span class="expand-value">{{ row.real_name || '-' }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">身份证号：</span>
        <span class="expand-value">{{ row.card_id || '-' }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">生日：</span>
        <span class="expand-value">{{ row.birthday || '-' }}</span>
      </el-col>
    </el-row>
    <el-row class="expand-row">
      <el-col :span="6">
        <span class="expand-key">地址：</span>
        <span class="expand-value">{{ row.addres || '-' }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">标签：</span>
        <span class="expand-value">{{ row.labels || '-' }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">推荐人：</span>
        <span class="expand-value">{{ row.spread_uid_nickname || '-' }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">推荐人ID：</span>
        <span class="expand-value">{{ row.spread_uid || '-' }}</span>
      </el-col>
    </el-row>
    <el-row class="expand-row">
      <el-col :span="6">
        <span class="expand-key">首次访问：</span>
        <span class="expand-value">{{ row.add_time | formatDate }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">近次访问：</span>
        <span class="expand-value">{{ row.last_time | formatDate }}</span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">分销状态：</span>
        <span class="expand-value">
          <el-tag :type="row.spread_open ? 'success' : 'danger'" size="mini">
            {{ row.spread_open ? '开启' : '禁用' }}
          </el-tag>
        </span>
      </el-col>
      <el-col :span="6">
        <span class="expand-key">积分：</span>
        <span class="expand-value">{{ row.integral || '0' }}</span>
      </el-col>
    </el-row>
    <el-row class="expand-row">
      <el-col :span="12">
        <span class="expand-key">备注：</span>
        <span class="expand-value">{{ row.mark || '-' }}</span>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { formatDate } from '@/utils/validate';
export default {
  name: 'table-expand',
  filters: {
    formatDate(time) {
      if (time !== 0) {
        let date = new Date(time * 1000);
        return formatDate(date, 'yyyy-MM-dd hh:mm');
      }
    },
  },
  props: {
    row: Object,
  },
};
</script>

<style lang="scss" scoped>
.expand-row {
  margin-bottom: 10px;

  .expand-key {
    font-weight: 600;
    color: #606266;
    margin-right: 8px;
  }

  .expand-value {
    color: #303133;
  }
}
</script>

<style scoped>
.expand-row {
  margin-bottom: 16px;
  /* margin-left: 20px; */
}
</style>
