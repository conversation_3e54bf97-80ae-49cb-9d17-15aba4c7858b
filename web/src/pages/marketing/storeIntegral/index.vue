<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="tableFrom"
          :model="tableFrom"
          :label-width="labelWidth"
          label-position="right"
          @submit.native.prevent
          inline
        >
          <el-form-item label="创建时间：">
            <el-date-picker
              clearable
              v-model="timeVal"
              type="daterange"
              :editable="false"
              @change="onchangeTime"
              format="yyyy/MM/dd"
              value-format="yyyy/MM/dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              style="width: 250px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="订单号：" label-for="order_sn">
            <el-input placeholder="请输入订单号" v-model="tableFrom.order_sn" class="form_content_width" />
          </el-form-item>
          <el-form-item label="用户信息：" label-for="user_keyword">
            <el-input placeholder="请输入用户昵称或手机号" v-model="tableFrom.user_keyword" class="form_content_width" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" v-db-click @click="userSearchs">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- 统计信息卡片 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <div class="statistics-container">
        <div class="stat-item">
          <div class="stat-value">{{ statistics.totalOrders || 0 }}</div>
          <div class="stat-label">积分抵扣订单数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ statistics.totalPointsUsed || 0 }}</div>
          <div class="stat-label">累计使用积分</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">￥{{ statistics.totalPointsMoney || 0 }}</div>
          <div class="stat-label">累计抵扣金额</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">￥{{ statistics.totalOrderAmount || 0 }}</div>
          <div class="stat-label">订单总金额</div>
        </div>
      </div>
    </el-card>

    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-table
        :data="tableList"
        v-loading="loading"
        highlight-current-row
        no-userFrom-text="暂无数据"
        no-filtered-userFrom-text="暂无筛选结果"
        class="mt14"
      >
        <el-table-column label="订单ID" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单号" min-width="160">
          <template slot-scope="scope">
            <el-tooltip placement="top" :open-delay="600">
              <div slot="content">{{ scope.row.orderSn }}</div>
              <span class="line1">{{ scope.row.orderSn }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="用户信息" min-width="120">
          <template slot-scope="scope">
            <div class="user-info">
              <img v-if="scope.row.userAvatar" :src="scope.row.userAvatar" class="user-avatar" />
              <div class="user-details">
                <div class="user-nickname">{{ scope.row.userNickname || '未知用户' }}</div>
                <div class="user-mobile">{{ scope.row.userMobile || '' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品信息" min-width="200">
          <template slot-scope="scope">
            <div class="goods-info">
              <div class="goods-count">共{{ scope.row.totalGoodsCount }}件商品</div>
              <el-tooltip placement="top" :open-delay="600">
                <div slot="content">{{ scope.row.goodsNames }}</div>
                <div class="goods-names line2">{{ scope.row.goodsNames }}</div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" min-width="120">
          <template slot-scope="scope">
            <div class="price-info">
              <div class="original-price">订单总价：￥{{ scope.row.orderPrice }}</div>
              <div class="actual-price">实付：￥{{ scope.row.actualPrice }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="积分抵扣" min-width="120">
          <template slot-scope="scope">
            <div class="points-info">
              <div class="points-count">积分：{{ scope.row.integral }}</div>
              <div class="points-money">抵扣：￥{{ scope.row.integralMoney }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="其他抵扣" min-width="120">
          <template slot-scope="scope">
            <div class="other-discount">
              <div v-if="scope.row.couponPrice > 0" class="coupon-price">优惠券：￥{{ scope.row.couponPrice }}</div>
              <div v-if="scope.row.balancePrice > 0" class="balance-price">余额：￥{{ scope.row.balancePrice }}</div>
              <div v-if="scope.row.savedAmount > 0" class="total-saved">共节省：￥{{ scope.row.savedAmount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" min-width="100">
          <template slot-scope="scope">
            <div class="status-info">
              <el-tag :type="getOrderStatusType(scope.row.orderStatus)" size="small">
                {{ scope.row.orderStatusText }}
              </el-tag>
              <el-tag :type="getPayStatusType(scope.row.payStatus)" size="small" class="mt5">
                {{ scope.row.payStatusText }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="140">
          <template slot-scope="scope">
            <div class="time-info">
              <div>下单：{{ formatTime(scope.row.createTime) }}</div>
              <div v-if="scope.row.payTime">支付：{{ formatTime(scope.row.payTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template slot-scope="scope">
            <a v-db-click @click="viewOrderDetail(scope.row)">查看详情</a>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="tableFrom.page"
          :limit.sync="tableFrom.limit"
          @pagination="getList"
        />
      </div>
    </el-card>
    
    <!-- 订单详情弹窗 -->
    <OrderDetailDialog
      v-if="showOrderDetail"
      :visible="showOrderDetail"
      :order-id="selectedOrderId"
      @close="closeOrderDetail"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getPointsOrderList, getPointsOrderStatistics } from '@/api/marketing';
import { formatDate } from '@/utils/validate';
import OrderDetailDialog from './components/OrderDetailDialog.vue';

export default {
  name: 'marketing_pointsOrders',
  components: {
    OrderDetailDialog,
  },
  filters: {
    formatDate(time) {
      if (time !== 0) {
        let date = new Date(time * 1000);
        return formatDate(date, 'yyyy-MM-dd');
      }
    },
  },
  data() {
    return {
      loading: false,
      pickerOptions: this.$timeOptions,
      tableList: [],
      timeVal: [],
      statistics: {
        totalOrders: 0,
        totalPointsUsed: 0,
        totalPointsMoney: 0,
        totalOrderAmount: 0,
        avgPointsPerOrder: 0,
        avgPointsMoneyPerOrder: 0,
      },
      tableFrom: {
        integral_time: '',
        order_sn: '',
        user_keyword: '',
        page: 1,
        limit: 15,
      },
      total: 0,
      showOrderDetail: false,
      selectedOrderId: null,
    };
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '80px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
  },
  activated() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    // 获取订单列表
    getList() {
      this.loading = true;
      const params = {
        page: this.tableFrom.page,
        limit: this.tableFrom.limit,
        orderSn: this.tableFrom.order_sn,
        userKeyword: this.tableFrom.user_keyword,
        startTime: this.timeVal && this.timeVal.length > 0 ? this.timeVal[0] : '',
        endTime: this.timeVal && this.timeVal.length > 1 ? this.timeVal[1] : '',
      };
      
      // 移除空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });
      
      getPointsOrderList(params)
        .then(async (res) => {
          if (res.data && res.data.list) {
            // 处理数据，添加计算属性
            this.tableList = res.data.list.map(order => ({
              ...order,
              savedAmount: this.calculateSavedAmount(order),
              goodsNames: this.getGoodsNames(order.goodsList),
              totalGoodsCount: this.getTotalGoodsCount(order.goodsList),
            }));
            this.total = res.data.count;
          } else {
            this.tableList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg || '获取数据失败');
          this.tableList = [];
          this.total = 0;
        });
    },
    
    // 获取统计信息
    getStatistics() {
      const params = {
        startTime: this.timeVal && this.timeVal.length > 0 ? this.timeVal[0] : '',
        endTime: this.timeVal && this.timeVal.length > 1 ? this.timeVal[1] : '',
      };
      
      // 移除空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });
      
      getPointsOrderStatistics(params)
        .then((res) => {
          if (res.data) {
            this.statistics = res.data;
          }
        })
        .catch((res) => {
          this.$message.error(res.msg || '获取统计信息失败');
        });
    },
    
    // 表格搜索
    userSearchs() {
      this.tableFrom.page = 1;
      this.getList();
      this.getStatistics();
    },
    
    // 重置搜索
    resetSearch() {
      this.tableFrom = {
        integral_time: '',
        order_sn: '',
        user_keyword: '',
        page: 1,
        limit: 15,
      };
      this.timeVal = [];
      this.getList();
      this.getStatistics();
    },
    
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.tableFrom.integral_time = this.timeVal ? this.timeVal.join('-') : '';
      this.getList();
      this.getStatistics();
    },
    
    // 查看订单详情
    viewOrderDetail(row) {
      this.selectedOrderId = row.id;
      this.showOrderDetail = true;
    },
    
    // 关闭订单详情
    closeOrderDetail() {
      this.showOrderDetail = false;
      this.selectedOrderId = null;
    },
    
    // 计算节省金额
    calculateSavedAmount(order) {
      let saved = 0;
      if (order.integralMoney) saved += parseFloat(order.integralMoney);
      if (order.couponPrice) saved += parseFloat(order.couponPrice);
      if (order.balancePrice) saved += parseFloat(order.balancePrice);
      return saved.toFixed(2);
    },
    
    // 获取商品名称
    getGoodsNames(goodsList) {
      if (!goodsList || goodsList.length === 0) return '';
      return goodsList.map(goods => goods.goodsName).join('、');
    },
    
    // 获取商品总数量
    getTotalGoodsCount(goodsList) {
      if (!goodsList || goodsList.length === 0) return 0;
      return goodsList.reduce((total, goods) => total + (goods.goodsNum || 0), 0);
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return '';
      const date = new Date(time);
      return date.toLocaleString('zh-CN', { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit', 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    },
    
    // 获取订单状态类型
    getOrderStatusType(status) {
      const statusMap = {
        0: 'info',     // 待支付
        1: 'warning',  // 待发货
        2: 'primary',  // 待收货
        3: 'success',  // 已完成
        4: 'danger',   // 已取消
        5: 'danger',   // 已退款
      };
      return statusMap[status] || 'info';
    },
    
    // 获取支付状态类型
    getPayStatusType(status) {
      const statusMap = {
        0: 'warning',  // 未支付
        1: 'success',  // 已支付
        2: 'danger',   // 已退款
      };
      return statusMap[status] || 'warning';
    },
  },
};
</script>

<style lang="scss" scoped>
.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.statistics-container {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  background: #f8f9fa;
  border-radius: 8px;
  
  .stat-item {
    text-align: center;
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  .user-details {
    .user-nickname {
      font-weight: 500;
      margin-bottom: 2px;
    }
    
    .user-mobile {
      font-size: 12px;
      color: #999;
    }
  }
}

.goods-info {
  .goods-count {
    font-size: 12px;
    color: #409eff;
    margin-bottom: 4px;
  }
  
  .goods-names {
    font-size: 13px;
    line-height: 1.4;
  }
}

.price-info {
  .original-price {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
  }
  
  .actual-price {
    font-size: 14px;
    color: #e6a23c;
    font-weight: 500;
  }
}

.points-info {
  .points-count {
    font-size: 12px;
    color: #67c23a;
    margin-bottom: 2px;
  }
  
  .points-money {
    font-size: 14px;
    color: #67c23a;
    font-weight: 500;
  }
}

.other-discount {
  font-size: 12px;
  
  .coupon-price {
    color: #f56c6c;
    margin-bottom: 2px;
  }
  
  .balance-price {
    color: #909399;
    margin-bottom: 2px;
  }
  
  .total-saved {
    color: #67c23a;
    font-weight: 500;
  }
}

.status-info {
  .el-tag {
    display: block;
    margin-bottom: 4px;
  }
}

.time-info {
  font-size: 12px;
  line-height: 1.4;
  
  div {
    margin-bottom: 2px;
  }
}

.line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
}

.mt5 {
  margin-top: 5px;
}

.mt14 {
  margin-top: 14px;
}

.mt16 {
  margin-top: 16px;
}

.form_content_width {
  width: 200px;
}

.padding-add {
  padding: 20px;
}

.acea-row {
  display: flex;
  align-items: center;
}

.row-right {
  justify-content: flex-end;
}

.page {
  margin-top: 20px;
}
</style>
