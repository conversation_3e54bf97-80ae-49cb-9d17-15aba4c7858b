<template>
  <el-dialog
    title="订单详情"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div v-if="orderDetail" class="order-detail">
        <!-- 订单基本信息 -->
        <div class="order-section">
          <h4>订单信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">订单号：</span>
                <span class="value">{{ orderDetail.orderSn }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">订单状态：</span>
                <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                  {{ orderDetail.orderStatusText }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 积分使用信息 -->
        <div class="order-section">
          <h4>积分使用详情</h4>
          <div class="points-detail">
            <div class="points-item">
              <span class="label">使用积分：</span>
              <span class="value points-count">{{ orderDetail.integral }} 积分</span>
            </div>
            <div class="points-item">
              <span class="label">抵扣金额：</span>
              <span class="value points-money">￥{{ orderDetail.integralMoney }}</span>
            </div>
          </div>
        </div>

        <!-- 价格信息 -->
        <div class="order-section">
          <h4>价格详情</h4>
          <div class="price-detail">
            <div class="price-item">
              <span class="label">商品总价：</span>
              <span class="value">￥{{ orderDetail.goodsPrice }}</span>
            </div>
            <div class="price-item">
              <span class="label">运费：</span>
              <span class="value">￥{{ orderDetail.freightPrice || 0 }}</span>
            </div>
            <div class="price-item">
              <span class="label">优惠券抵扣：</span>
              <span class="value discount">-￥{{ orderDetail.couponPrice || 0 }}</span>
            </div>
            <div class="price-item">
              <span class="label">积分抵扣：</span>
              <span class="value discount">-￥{{ orderDetail.integralMoney }}</span>
            </div>
            <div class="price-item">
              <span class="label">余额抵扣：</span>
              <span class="value discount">-￥{{ orderDetail.balancePrice || 0 }}</span>
            </div>
            <div class="price-item total">
              <span class="label">实付金额：</span>
              <span class="value">￥{{ orderDetail.actualPrice }}</span>
            </div>
          </div>
        </div>

        <!-- 商品列表 -->
        <div class="order-section">
          <h4>商品信息</h4>
          <el-table :data="orderDetail.goodsList" border>
            <el-table-column label="商品名称" prop="goodsName" min-width="200" />
            <el-table-column label="规格" prop="goodsSpec" width="150" />
            <el-table-column label="单价" width="100">
              <template slot-scope="scope">
                ￥{{ scope.row.goodsPrice }}
              </template>
            </el-table-column>
            <el-table-column label="数量" prop="goodsNum" width="80" />
            <el-table-column label="小计" width="100">
              <template slot-scope="scope">
                ￥{{ (scope.row.goodsPrice * scope.row.goodsNum).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    
    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'OrderDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      orderDetail: null,
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val && this.orderId) {
        this.fetchOrderDetail();
      }
    },
  },
  methods: {
    async fetchOrderDetail() {
      this.loading = true;
      try {
        // 这里应该调用获取订单详情的API
        // 暂时模拟数据
        setTimeout(() => {
          this.orderDetail = {
            orderSn: 'ORDER' + this.orderId,
            orderStatus: 1,
            orderStatusText: '待发货',
            integral: 1000,
            integralMoney: 10.00,
            goodsPrice: 100.00,
            freightPrice: 5.00,
            couponPrice: 0.00,
            balancePrice: 0.00,
            actualPrice: 95.00,
            goodsList: [
              {
                goodsName: '测试商品',
                goodsSpec: '红色/L',
                goodsPrice: 100.00,
                goodsNum: 1,
              },
            ],
          };
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        this.$message.error('获取订单详情失败');
      }
    },
    
    handleClose() {
      this.dialogVisible = false;
      this.$emit('close');
    },
    
    getOrderStatusType(status) {
      const statusMap = {
        0: 'info',
        1: 'warning',
        2: 'primary',
        3: 'success',
        4: 'danger',
        5: 'danger',
      };
      return statusMap[status] || 'info';
    },
  },
};
</script>

<style lang="scss" scoped>
.order-detail {
  .order-section {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 16px;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }
  }
  
  .info-item, .points-item, .price-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .label {
      color: #606266;
      margin-right: 16px;
    }
    
    .value {
      color: #303133;
      
      &.points-count {
        color: #67c23a;
        font-weight: 500;
      }
      
      &.points-money {
        color: #67c23a;
        font-weight: 500;
      }
      
      &.discount {
        color: #f56c6c;
      }
    }
    
    &.total {
      border-top: 1px solid #ebeef5;
      padding-top: 8px;
      margin-top: 8px;
      font-weight: 500;
      
      .value {
        color: #e6a23c;
        font-size: 16px;
      }
    }
  }
}
</style>