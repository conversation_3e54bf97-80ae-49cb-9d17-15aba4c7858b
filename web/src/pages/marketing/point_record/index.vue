<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="formValidate"
          :model="formValidate"
          :label-width="labelWidth"
          :label-position="labelPosition"
          @submit.native.prevent
          inline
        >
          <el-form-item label="订单时间：">
            <el-date-picker
              clearable
              v-model="timeVal"
              type="daterange"
              :editable="false"
              @change="onchangeTime"
              format="yyyy/MM/dd"
              value-format="yyyy/MM/dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              style="width: 250px"
              class="mr20"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="交易类型：">
            <el-select
              type="button"
              v-model="formValidate.trading_type"
              @change="selChange"
              class="form_content_width"
              clearable
            >
              <el-option
                :label="item"
                :value="Object.keys(withdrawal)[index]"
                v-for="(item, index) in Object.values(withdrawal)"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card :bordered="false" shadow="never" class="mt16">
      <el-table ref="table" :data="tabList" class="ivu-mt" v-loading="loading" empty-text="暂无数据">
        <el-table-column label="ID" min-width="70">
          <template slot-scope="scope">
            <div>{{ scope.row.id }}</div>
          </template>
        </el-table-column>
        <el-table-column label="关联订单" min-width="100">
          <template slot-scope="scope">
            <div 
              v-if="scope.row.relation && scope.row.relation !== '-'"
              class="order-link"
              @click="handleRelationClick(scope.row)"
            >
              {{ getRelationText(scope.row) }}
            </div>
            <div v-else>{{ scope.row.relation }}</div>
          </template>
        </el-table-column>
        <el-table-column label="交易时间" min-width="100">
          <template slot-scope="scope">
            <div>{{ scope.row.add_time }}</div>
          </template>
        </el-table-column>
        <el-table-column label="交易积分" min-width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.pm" class="z-price">+ {{ scope.row.number }}</div>
            <div v-else class="f-price">- {{ scope.row.number }}</div>
          </template>
        </el-table-column>
        <el-table-column label="用户" min-width="80">
          <template slot-scope="scope">
            <div>{{ scope.row.nickname }}</div>
          </template>
        </el-table-column>
        <el-table-column label="交易类型" min-width="100">
          <template slot-scope="scope">
            <div>{{ scope.row.type_name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="备注" min-width="100">
          <template slot-scope="scope">
            <div>{{ scope.row.mark }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <a class="item" v-db-click @click="setMark(scope.row)">修改备注</a>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="formValidate.page"
          :limit.sync="formValidate.limit"
          @pagination="getList"
        />
      </div>
    </el-card>
    <!-- 拒绝通过-->
    <el-dialog :visible.sync="modals" title="备注" :close-on-click-modal="false" width="470px">
      <el-input v-model="mark_msg.mark" type="textarea" :rows="4" placeholder="请输入备注" />
      <div slot="footer">
        <el-button type="primary" size="small" v-db-click @click="oks">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 订单详情弹窗 -->
    <el-dialog :visible.sync="orderDetailModal" title="订单详情" width="720px" class="order-detail-dialog">
      <order-detail v-if="orderDetailModal && currentOrderId" :orderId="currentOrderId" />
    </el-dialog>
    
    <!-- 推广用户信息弹窗 -->
    <el-dialog :visible.sync="promotionUserModal" title="推广用户信息" width="600px" class="promotion-user-dialog">
      <promotion-user-info v-if="promotionUserModal && currentPromotionUserId" :userId="currentPromotionUserId" />
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { pointRecordList, setPointRecordMark } from '@/api/marketing';
import { formatDate } from '@/utils/validate';
import dateRadio from '@/components/dateRadio';
import OrderDetail from './components/OrderDetail.vue';
import PromotionUserInfo from './components/PromotionUserInfo.vue';
export default {
  name: 'pointRecord',
  components: { dateRadio, OrderDetail, PromotionUserInfo },
  filters: {
    formatDate(time) {
      if (time !== 0) {
        let date = new Date(time * 1000);
        return formatDate(date, 'yyyy-MM-dd hh:mm');
      }
    },
  },
  data() {
    return {
      images: ['1.jpg', '2.jpg'],
      modal_loading: false,
      pickerOptions: this.$timeOptions,
      mark_msg: {
        mark: '',
      },
      modals: false,
      total: 0,
      loading: false,
      tabList: [],
      withdrawal: [],
      selectIndexTime: '',
      formValidate: {
        trading_type: '',
        time: '',
        keywords: '',
        page: 1,
        limit: 20,
      },
      timeVal: [],
      FromData: null,
      extractId: 0,
      // 订单详情相关
      orderDetailModal: false,
      currentOrderId: null,
      // 推广用户信息相关
      promotionUserModal: false,
      currentPromotionUserId: null,
    };
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '80px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 确定
    oks() {
      this.modal_loading = true;
      this.mark_msg.mark = this.mark_msg.mark.trim();
      setPointRecordMark(this.extractId, this.mark_msg)
        .then(async (res) => {
          this.$message.success(res.msg);
          this.modal_loading = false;
          this.modals = false;
          this.getList();
        })
        .catch((res) => {
          this.modal_loading = false;
          this.$message.error(res.msg);
        });
    },
    // 备注
    setMark(row) {
      this.modals = true;
      this.extractId = row.id;
      this.mark_msg.mark = row.mark;
    },
    onSelectDate(e) {
      this.formValidate.time = e;
      this.formValidate.page = 1;
      this.getList();
    },
    dateToMs(date) {
      let result = new Date(date).getTime();
      return result;
    },
    // 具体日期
    onchangeTime(e) {
      this.timeVal = e;
      this.formValidate.time = this.timeVal ? this.timeVal.join('-') : '';
      this.formValidate.page = 1;
      this.getList();
    },
    // 选择
    selChange(e) {
      this.formValidate.page = 1;
      this.formValidate.trading_type = e;
      this.getList();
    },
    // 列表
    getList() {
      this.loading = true;
      pointRecordList(this.formValidate)
        .then(async (res) => {
          let data = res.data;
          this.tabList = data.list;
          this.total = data.count;
          this.withdrawal = data.status;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg);
        });
    },
    // 编辑提交成功
    submitFail() {
      this.getList();
    },
    // 处理关联点击事件
    handleRelationClick(row) {
      // 判断是订单还是推广用户
      if (this.isPromotionRecord(row)) {
        this.viewPromotionUserInfo(row);
      } else {
        this.viewOrderDetail(row);
      }
    },
    // 判断是否为推广积分记录
    isPromotionRecord(row) {
      // 根据 relation 字段判断
      return row.relation && row.relation.includes('推广用户:');
    },
    // 获取关联显示文本
    getRelationText(row) {
      if (this.isPromotionRecord(row)) {
        return '查看被推广用户';
      }
      return row.relation;
    },
    // 查看推广用户信息
    viewPromotionUserInfo(row) {
      // 从 relation中提取用户ID（格式为 "推广用户:ID"）
      let userId = null;
      
      if (row.relation && row.relation.includes('推广用户:')) {
        const idStr = row.relation.replace('推广用户:', '').trim();
        if (idStr && !isNaN(idStr)) {
          userId = parseInt(idStr);
        }
      }
      
      if (userId) {
        this.currentPromotionUserId = userId;
        this.promotionUserModal = true;
      } else {
        this.$message.warning('无法获取被推广用户ID');
      }
    },
    // 查看订单详情
    viewOrderDetail(row) {
      // 从 relation中提取订单ID
      if (row.relation && row.relation.includes('订单:')) {
        const orderId = row.relation.replace('订单:', '').trim();
        if (orderId && orderId !== '-') {
          this.currentOrderId = orderId;
          this.orderDetailModal = true;
        } else {
          this.$message.warning('无法获取订单ID');
        }
      } else {
        this.$message.warning('该记录无关联订单');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ivu-mt .type .item {
  margin: 3px 0;
}
.Refresh {
  font-size: 12px;
  color: var(--prev-color-primary);
  cursor: pointer;
}
.ivu-form-item {
  margin-bottom: 10px;
}
.status ::v-deep .item ~ .item {
  margin-left: 6px;
}
.status ::v-deep .statusVal {
  margin-bottom: 7px;
}

/* .ivu-mt ::v-deep .ivu-table-header */
/* border-top:1px dashed #ddd!important */
.type {
  padding: 3px 0;
  box-sizing: border-box;
}
.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}
.z-price {
  color: red;
}
.f-price {
  color: green;
}

.order-link {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
  
  &:hover {
    color: #66b1ff;
  }
}

::v-deep .order-detail-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
