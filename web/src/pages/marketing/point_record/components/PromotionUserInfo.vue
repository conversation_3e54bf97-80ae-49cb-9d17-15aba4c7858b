<template>
  <div class="promotion-user-info" v-if="userInfo" v-loading="loading">
    <div class="user-section">
      <div class="user-header">
        <div class="user-avatar">
          <img 
            v-if="userInfo.avatar" 
            :src="userInfo.avatar" 
            alt="用户头像"
            @error="handleImageError"
          />
          <div class="default-avatar" :class="{ 'show': !userInfo.avatar }">
            <i class="el-icon-user-solid"></i>
          </div>
        </div>
        <div class="user-basic">
          <h3 class="user-nickname">{{ userInfo.nickname || '未设置昵称' }}</h3>
          <p class="user-id">用户ID: {{ userInfo.id }}</p>
        </div>
      </div>
    </div>

    <div class="info-section">
      <div class="section-title">基本信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">手机号码：</span>
          <span class="value">{{ userInfo.mobile || '未绑定' }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前积分：</span>
          <span class="value points-value">{{ userInfo.points }}积分</span>
        </div>
        <div class="info-item">
          <span class="label">注册时间：</span>
          <span class="value">{{ formatTime(userInfo.registerTime) }}</span>
        </div>
        <div class="info-item">
          <span class="label">最后登录：</span>
          <span class="value">{{ formatTime(userInfo.lastLoginTime) }}</span>
        </div>
      </div>
    </div>

    <div class="info-section" v-if="userInfo.promoterId">
      <div class="section-title">推广信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">推广者：</span>
          <span class="value">{{ userInfo.promoterNickname || '未知' }}</span>
        </div>
        <div class="info-item">
          <span class="label">推广时间：</span>
          <span class="value">{{ formatTime(userInfo.promotionTime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPromotionUserInfo } from '@/api/marketing';

export default {
  name: 'PromotionUserInfo',
  props: {
    userId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      userInfo: null
    };
  },
  watch: {
    userId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getUserInfo();
        }
      }
    }
  },
  methods: {
    async getUserInfo() {
      if (!this.userId) return;
      
      this.loading = true;
      try {
        const response = await getPromotionUserInfo(this.userId);
        if (response && response.data) {
          this.userInfo = response.data;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        this.$message.error('获取用户信息失败');
      } finally {
        this.loading = false;
      }
    },
    
    formatTime(time) {
      if (!time) return '暂无记录';
      
      // 如果是时间戳（数字）
      if (typeof time === 'number') {
        return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
      }
      
      // 如果是日期字符串
      if (typeof time === 'string') {
        return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
      }
      
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
    },
    
    handleImageError(event) {
      // 图片加载失败时显示默认头像
      event.target.style.display = 'none';
      const defaultAvatar = event.target.parentElement.querySelector('.default-avatar');
      if (defaultAvatar) {
        defaultAvatar.classList.add('show');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.promotion-user-info {
  .user-section {
    margin-bottom: 20px;
    
    .user-header {
      display: flex;
      align-items: center;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      color: white;
      
      .user-avatar {
        width: 60px;
        height: 60px;
        margin-right: 16px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid rgba(255, 255, 255, 0.3);
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .default-avatar {
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.2);
          display: none;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: rgba(255, 255, 255, 0.8);
          
          &.show {
            display: flex;
          }
        }
      }
      
      .user-basic {
        flex: 1;
        
        .user-nickname {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 500;
        }
        
        .user-id {
          margin: 0;
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }
  }
  
  .info-section {
    margin-bottom: 20px;
    border: 1px solid #e8eaed;
    border-radius: 6px;
    
    .section-title {
      background-color: #f8f9fa;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid #e8eaed;
    }
    
    .info-grid {
      padding: 16px;
      
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #909399;
          font-size: 13px;
          margin-right: 8px;
          min-width: 80px;
        }
        
        .value {
          color: #606266;
          font-size: 13px;
          
          &.points-value {
            color: #e6a23c;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>