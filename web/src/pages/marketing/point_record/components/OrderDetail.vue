<template>
  <div class="order-detail" v-if="orderDetail.orderInfo" v-loading="loading">
    <div class="section">
      <div class="section-title">收货信息</div>
      <div class="section-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">用户昵称：</span>
            <span class="value">{{ orderDetail.userInfo ? orderDetail.userInfo.nickname : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货人：</span>
            <span class="value">{{ orderDetail.orderInfo.consignee || '-' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ orderDetail.orderInfo.mobile || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货地址：</span>
            <span class="value">{{ orderDetail.orderInfo.address || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">订单信息</div>
      <div class="section-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">订单编号：</span>
            <span class="value">{{ orderDetail.orderInfo.order_sn || orderDetail.orderInfo.id }}</span>
          </div>
          <div class="info-item status-item">
            <span class="label">订单状态：</span>
            <span class="value status-value">{{ getOrderStatusText(orderDetail.orderInfo.order_status) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">商品总价：</span>
            <span class="value">¥{{ orderDetail.orderInfo.goods_price || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">实付金额：</span>
            <span class="value price-highlight">¥{{ orderDetail.orderInfo.actual_price || 0 }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">积分抵扣：</span>
            <span class="value">{{ orderDetail.orderInfo.integral || 0 }}积分 (¥{{ orderDetail.orderInfo.integral_money || 0 }})</span>
          </div>
          <div class="info-item">
            <span class="label">余额支付：</span>
            <span class="value">¥{{ orderDetail.orderInfo.balance_price || 0 }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatTime(orderDetail.orderInfo.create_time) }}</span>
          </div>
          <div class="info-item" v-if="orderDetail.orderInfo.pay_time">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatTime(orderDetail.orderInfo.pay_time) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="section" v-if="orderDetail.orderInfo.cartInfo && orderDetail.orderInfo.cartInfo.length > 0">
      <div class="section-title">商品信息</div>
      <div class="goods-list">
        <el-table :data="orderDetail.orderInfo.cartInfo" border>
          <el-table-column label="商品ID" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.product_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" min-width="200">
            <template slot-scope="scope">
              <div class="goods-info">
                <img 
                  v-if="scope.row.productInfo && scope.row.productInfo.image" 
                  :src="scope.row.productInfo.image" 
                  alt="" 
                  class="goods-image"
                />
                <div class="goods-text">
                  <p class="goods-name">{{ scope.row.productInfo ? scope.row.productInfo.store_name : scope.row.product_name }}</p>
                  <p class="goods-spec" v-if="scope.row.sku">{{ scope.row.sku }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="单价" width="100">
            <template slot-scope="scope">
              <span>¥{{ scope.row.price }}</span>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.cart_num }}</span>
            </template>
          </el-table-column>
          <el-table-column label="小计" width="100">
            <template slot-scope="scope">
              <span>¥{{ (scope.row.price * scope.row.cart_num).toFixed(2) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getDataInfo } from '@/api/order';

export default {
  name: 'OrderDetail',
  props: {
    orderId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      orderDetail: {
        orderInfo: null,
        userInfo: null
      }
    };
  },
  watch: {
    orderId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getOrderDetail();
        }
      }
    }
  },
  methods: {
    async getOrderDetail() {
      if (!this.orderId) return;
      
      this.loading = true;
      try {
        const response = await getDataInfo(this.orderId);
        if (response && response.data) {
          this.orderDetail = response.data;
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
        this.$message.error('获取订单详情失败');
      } finally {
        this.loading = false;
      }
    },
    
    getOrderStatusText(status) {
      const statusMap = {
        0: '待付款',
        1: '待发货', 
        2: '待收货',
        3: '待评价',
        4: '已完成',
        5: '已取消',
        6: '已退款'
      };
      return statusMap[status] || '未知状态';
    },
    
    formatTime(time) {
      if (!time) return '-';
      
      // 如果是时间戳（数字）
      if (typeof time === 'number') {
        return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
      }
      
      // 如果是日期字符串
      if (typeof time === 'string') {
        return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
      }
      
      return time;
    }
  }
};
</script>

<style lang="scss" scoped>
.order-detail {
  .section {
    margin-bottom: 20px;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    
    .section-title {
      background-color: #f8f9fa;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      border-bottom: 1px solid #e8eaed;
    }
    
    .section-content {
      padding: 16px;
      
      .info-row {
        display: flex;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-item {
          flex: 1;
          display: flex;
          align-items: center;
          
          .label {
            color: #909399;
            font-size: 13px;
            margin-right: 8px;
            min-width: 80px;
          }
          
          .value {
            color: #606266;
            font-size: 13px;
          }
          
          &.status-item .status-value {
            color: #e6a23c;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .goods-list {
    padding: 16px;
    
    .goods-info {
      display: flex;
      align-items: center;
      
      .goods-image {
        width: 50px;
        height: 50px;
        border-radius: 4px;
        margin-right: 12px;
        object-fit: cover;
      }
      
      .goods-text {
        flex: 1;
        
        .goods-name {
          font-size: 14px;
          color: #303133;
          margin: 0 0 4px 0;
          line-height: 1.4;
        }
        
        .goods-spec {
          font-size: 12px;
          color: #909399;
          margin: 0;
        }
      }
    }
  }
  
  .price-highlight {
    color: #f56c6c !important;
    font-weight: 500;
  }
}
</style>