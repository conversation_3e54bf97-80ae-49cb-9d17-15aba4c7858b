<template>
  <div v-loading="spinShow" element-loading-text="加载中..." element-loading-spinner="el-icon-loading">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-coin"></i>
        积分订单管理
      </h2>
      <p class="page-description">管理和查看使用积分兑换的订单信息</p>
    </div>
    
    <productlist-details
      class="ivu-mt"
      v-if="currentTab === 'article' || 'project' || 'app'"
      ref="productlist"
      @loading-change="handleLoadingChange"
    ></productlist-details>
    
    <!-- 错误提示 -->
    <el-alert
      v-if="errorMessage"
      :title="errorMessage"
      type="error"
      :closable="true"
      @close="clearError"
      class="error-alert"
    >
    </el-alert>
  </div>
</template>

<script>
import productlistDetails from './orderlistDetails';
import { mapMutations } from 'vuex';
export default {
  name: 'list',
  components: {
    productlistDetails,
  },
  data() {
    return {
      spinShow: false,
      currentTab: '',
      data: [],
      tablists: null,
      errorMessage: '',
    };
  },
  created() {
    this.getOrderType('');
    this.getOrderStatus('');
    this.getOrderTime('');
    this.getOrderNum('');
    this.getfieldKey('');
    this.onChangeTabs('');
  },
  beforeDestroy() {
    this.getOrderType('');
    this.getOrderStatus('');
    this.getOrderTime('');
    this.getOrderNum('');
    this.getfieldKey('');
    this.onChangeTabs('');
  },
  mounted() {
    this.getTabs();
  },
  methods: {
    ...mapMutations('integralOrder', [
      'onChangeTabs',
      'getOrderStatus',
      'getOrderTime',
      'getOrderNum',
      'getfieldKey',
      'getOrderType',
    ]),
    // 处理加载状态变化
    handleLoadingChange(loading) {
      this.spinShow = loading;
    },
    // 清除错误信息
    clearError() {
      this.errorMessage = '';
    },
    // 显示错误信息
    showError(message) {
      this.errorMessage = message;
      // 5秒后自动清除
      setTimeout(() => {
        this.errorMessage = '';
      }, 5000);
    },
    // 订单类型  @on-changeTabs="getChangeTabs"
    getTabs() {
      this.spinShow = true;
      this.clearError();
      
      this.$store
        .dispatch('integralOrder/getOrderTabs', {
          data: '',
        })
        .then((res) => {
          this.tablists = res.data;
          this.spinShow = false;
        })
        .catch((res) => {
          this.spinShow = false;
          const errorMsg = res?.msg || '获取订单标签失败';
          this.showError(errorMsg);
          this.$message.error(errorMsg);
        });
    },
    onClickTab() {
      try {
        this.onChangeTabs(Number(this.currentTab));
        this.$store.dispatch('integralOrder/getOrderTabs', {
          data: '',
          type: Number(this.currentTab),
        });
        this.$refs.productlist.getChangeTabs();
      } catch (error) {
        console.error('切换标签失败:', error);
        this.showError('切换标签失败，请重试');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 28px;
    }
  }
  
  .page-description {
    margin: 8px 0 0 0;
    font-size: 14px;
    opacity: 0.9;
  }
}

.error-alert {
  margin-bottom: 16px;
}

.product_tabs ::v-deep .ivu-tabs-bar {
  margin-bottom: 0px !important;
}
.product_tabs ::v-deep .ivu-page-header-content {
  margin-bottom: 0px !important;
}
.product_tabs ::v-deep .ivu-page-header-breadcrumb {
  margin-bottom: 0px !important;
}
</style>
