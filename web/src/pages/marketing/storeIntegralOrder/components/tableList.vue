<template>
  <div>
    <el-table
      :data="orderList"
      ref="table"
      v-loading="loading"
      highlight-current-row
      empty-text="暂无数据"
      @select="selectAll"
      @select-all="selectAll"
      class="orderData"
    >
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column label="订单号" min-width="150">
        <template slot-scope="scope">
          <span v-text="scope.row.order_id" style="display: block"></span>
          <span v-if="scope.row.is_del == 1" style="color: #ed4014; display: block">用户已删除</span>
        </template>
      </el-table-column>
      <el-table-column label="用户信息" min-width="100">
        <template slot-scope="scope"> {{ scope.row.nickname }}/{{ scope.row.uid }} </template>
      </el-table-column>
      <el-table-column label="商品信息" min-width="330">
        <template slot-scope="scope">
          <div class="tabBox">
            <div class="tabBox_img" v-viewer>
              <img v-lazy="scope.row.image" :alt="scope.row.store_name" />
            </div>
            <div class="tabBox_content">
              <span class="tabBox_tit">{{ scope.row.store_name }}</span>
              <span class="tabBox_spec" v-if="scope.row.suk">{{ scope.row.suk }}</span>
              <span class="tabBox_pice">
                <span class="integral-badge">积分 {{ scope.row.total_price }}</span>
                <span class="quantity">x {{ scope.row.total_num }}</span>
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="兑换积分" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.total_price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.statusColor" size="small">
            {{ scope.row.statusText || scope.row.status_name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.add_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="180">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button 
              size="mini" 
              type="primary" 
              v-if="scope.row.status === 1"
              @click="sendOrder(scope.row)"
            >
              发货
            </el-button>
            <el-button 
              size="mini" 
              type="info" 
              v-if="scope.row.status === 2"
              @click="delivery(scope.row)"
            >
              物流
            </el-button>
            <el-dropdown size="small" @command="changeMenu(scope.row, $event)" :transfer="true">
              <el-button size="mini" type="text">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="2">
                  <i class="el-icon-view"></i> 订单详情
                </el-dropdown-item>
                <el-dropdown-item command="3">
                  <i class="el-icon-time"></i> 订单记录
                </el-dropdown-item>
                <el-dropdown-item command="4" v-show="scope.row.status !== 4">
                  <i class="el-icon-edit"></i> 订单备注
                </el-dropdown-item>
                <el-dropdown-item command="5" v-show="scope.row.status === 1">
                  <i class="el-icon-user"></i> 用户信息
                </el-dropdown-item>
                <el-dropdown-item command="8" v-show="scope.row.status === 2">
                  <i class="el-icon-check"></i> 确认收货
                </el-dropdown-item>
                <el-dropdown-item command="11" v-show="scope.row.status >= 1 && scope.row.express_dump">
                  <i class="el-icon-printer"></i> 打印面单
                </el-dropdown-item>
                <el-dropdown-item command="9" v-show="scope.row.is_del === 1" divided>
                  <i class="el-icon-delete" style="color: #f56c6c"></i> 删除订单
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="acea-row row-right page">
      <pagination
        v-if="total"
        :total="total"
        :page.sync="page.pageNum"
        :limit.sync="page.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 编辑 退款 退积分 不退款-->
    <edit-from ref="edits" :FromData="FromData" @submitFail="submitFail"></edit-from>
    <!-- 会员详情-->
    <user-details ref="userDetails"></user-details>
    <!-- 详情 -->
    <details-from ref="detailss" :orderDatalist="orderDatalist" :orderId="orderId"></details-from>
    <!-- 备注 -->
    <order-remark ref="remarks" :orderId="orderId" @submitFail="submitFail"></order-remark>
    <!-- 记录 -->
    <order-record ref="record"></order-record>
    <!-- 发送货 -->
    <order-send ref="send" :orderId="orderId" @submitFail="submitFail"></order-send>
  </div>
</template>

<script>
import expandRow from './tableExpand.vue';
import {
  orderList,
  getOrdeDatas,
  getDataInfo,
  getRefundFrom,
  getnoRefund,
  refundIntegral,
  getDistribution,
  writeUpdate,
} from '@/api/order';
import { getIntegralOrderDataInfo, integralOrderList, getIntegralOrderDistribution } from '@/api/marketing';
import { mapState, mapMutations } from 'vuex';
import editFrom from '../../../../components/from/from';
import detailsFrom from '../handle/orderDetails';
import orderRemark from '../handle/orderRemark';
import orderRecord from '../handle/orderRecord';
import orderSend from '../handle/orderSend';
import userDetails from '@/pages/user/list/handle/userDetails';

export default {
  name: 'table_list',
  components: {
    expandRow,
    editFrom,
    detailsFrom,
    orderRemark,
    orderRecord,
    orderSend,
    userDetails,
  },
  props: ['where', 'isAll'],
  data() {
    return {
      delfromData: {},
      modal: false,
      orderList: [],
      orderCards: [],
      loading: false,
      orderId: 0,
      total: 0, // 总条数
      page: {
        pageNum: 1, // 当前页
        pageSize: 10, // 每页显示条数
      },
      data: [],
      FromData: null,
      orderDatalist: null,
      modalTitleSs: '',
      isDelIdList: [],
      checkBox: false,
      formSelection: [],
      selectionCopy: [],
      display: 'none',
      autoDisabled: false,
      // isAll: -1,
    };
  },
  computed: {
    ...mapState('integralOrder', ['orderPayType', 'orderStatus', 'orderTime', 'orderNum', 'fieldKey', 'orderType']),
  },
  mounted() {
    this.getList();
  },
  activated() {
    this.getList();
  },
  watch: {
    orderType: function () {
      this.page.pageNum = 1;
      this.getList();
    },
    formSelection(value) {
      this.$emit('order-select', value);
      if (value.length) {
        this.$emit('auto-disabled', 0);
      } else {
        this.$emit('auto-disabled', 1);
      }
      let isDel = value.some((item) => {
        return item.is_del === 1;
      });
      this.getIsDel(isDel);
      this.getisDelIdListl(value);
    },
    orderList: {
      deep: true,
      handler(value) {
        value.forEach((item) => {
          this.formSelection.forEach((itm) => {
            if (itm.id === item.id) {
              item.checkBox = true;
            }
          });
        });
        const arr = this.orderList.filter((item) => item.checkBox);
        if (this.orderList.length) {
          this.checkBox = this.orderList.length === arr.length;
        } else {
          this.checkBox = false;
        }
      },
    },
  },
  methods: {
    ...mapMutations('integralOrder', ['getIsDel', 'getisDelIdListl']),
    selectAll(row) {
      if (row.length) {
        this.formSelection = row;
        this.selectionCopy = row;
      }
      this.selectionCopy.forEach((item, index) => {
        item.checkBox = this.checkBox;
        this.$set(this.orderList, index, item);
      });
    },
    showUserInfo(row) {
      this.$refs.userDetails.modals = true;
      this.$refs.userDetails.getDetails(row.uid);
    },
    // 操作
    changeMenu(row, name) {
      this.orderId = row.id;
      switch (name) {
        case '2':
          this.getData(row.id);
          break;
        case '3':
          this.$refs.record.modals = true;
          this.$refs.record.getList(row.id);
          break;
        case '4':
          this.$refs.remarks.modals = true;
          this.$refs.remarks.formValidate.remark = row.remark;
          break;
        case '5':
          this.showUserInfo(row);
          break;
        case '8':
          this.$confirm('确认订单已收货？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.delfromData = {
              title: '修改确认收货',
              url: `marketing/integral/order/take/${row.id}`,
              method: 'put',
              ids: '',
            };
            this.$modalSure(this.delfromData)
              .then((res) => {
                this.$message.success(res.msg);
                this.getList();
              })
              .catch((res) => {
                this.$message.error(res.msg);
              });
          }).catch(() => {
            this.$message.info('已取消操作');
          });
          break;
        case '10':
          this.delfromData = {
            title: '立即打印订单',
            info: '您确认打印此订单吗?',
            url: `marketing/integral/order/print/${row.id}`,
            method: 'get',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.$emit('changeGetTabs');
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          break;
        case '11':
          this.delfromData = {
            title: '立即打印电子面单',
            info: '您确认打印此电子面单吗?',
            url: `/order/order_dump/${row.id}`,
            method: 'get',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          break;
        default:
          this.delfromData = {
            title: '删除订单',
            url: `marketing/integral/order/del/${row.id}`,
            method: 'DELETE',
            ids: '',
          };
          this.delOrder(row, this.delfromData);
      }
    },
    // 立即支付 /确认收货//删除单条订单
    submitModel() {
      this.getList();
    },
    // 订单列表
    getList(res) {
      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;
      this.loading = true;
      
      const params = {
        page: this.page.pageNum,
        limit: this.page.pageSize,
        status: this.orderStatus,
        pay_type: this.orderPayType,
        data: this.orderTime,
        real_name: this.orderNum,
        field_key: this.fieldKey,
        type: this.orderType === 0 ? '' : this.orderType,
        product_id: this.$route.query.product_id,
        min_integral: this.where?.min_integral,
        max_integral: this.where?.max_integral,
      };
      
      integralOrderList(params)
        .then(async (res) => {
          if (res.success && res.data) {
            let data = res.data;
            
            // 适配接口返回的数据结构
            const orderList = data.list || data.data || [];
            
            this.orderList = orderList.map((item) => {
              // 设置选择状态
              if (this.isAll === 1) {
                item.checkBox = true;
              } else {
                item.checkBox = false;
              }
              
              // 数据格式化和字段映射
              const mappedItem = {
                ...item,
                // 映射字段名称
                id: item.id,
                order_id: item.orderSn || item.order_id,
                uid: item.userId || item.uid,
                nickname: item.userNickname || item.nickname || '未知用户',
                
                // 商品信息处理
                store_name: item.goodsNames || (item.goodsList && item.goodsList[0]?.goodsName) || item.store_name || '未知商品',
                image: (item.goodsList && item.goodsList[0]?.listPicUrl) || item.image || '/default-product.png',
                suk: (item.goodsList && item.goodsList[0]?.goodsSpecificationNameValue) || item.suk || '',
                
                // 积分和价格信息
                total_price: item.integral || item.total_price || 0,
                total_num: (item.goodsList && item.goodsList[0]?.number) || item.total_num || 1,
                
                // 状态信息
                status: item.orderStatus !== undefined ? item.orderStatus : item.status,
                status_name: item.orderStatusText || item.status_name || this.getStatusText(item.orderStatus || item.status),
                
                // 时间信息
                add_time: item.createTimeFormat || item.add_time || item.createTime,
                
                // 其他字段保持原样
                is_del: item.is_del || 0,
                express_dump: item.express_dump,
                remark: item.remark || '',
              };
              
              // 数据格式化
              mappedItem.statusText = this.getStatusText(mappedItem.status);
              mappedItem.statusColor = this.getStatusColor(mappedItem.status);
              
              return mappedItem;
            });
            
            // 统计信息处理
            this.orderCards = data.stat || this.generateStats(orderList);
            this.total = data.count || data.total || orderList.length;
            
            this.$emit('on-changeCards', this.orderCards);
            this.loading = false;
            
            console.log('订单列表加载成功:', {
              totalCount: this.total,
              currentPageCount: this.orderList.length,
              stats: this.orderCards
            });
          } else {
            throw new Error(res.msg || '数据格式错误');
          }
        })
        .catch((error) => {
          this.loading = false;
          console.error('获取订单列表失败:', error);
          const errorMsg = error?.response?.data?.msg || error?.msg || error.message || '获取订单列表失败';
          this.$message.error(errorMsg);
        });
    },
    // 全选
    onSelectTab(selection) {
      this.formSelection = selection;
      let isDel = selection.some((item) => {
        return item.is_del === 1;
      });
      this.getIsDel(isDel);
      this.getisDelIdListl(selection);
    },
    // 编辑
    edit(row) {
      this.getOrderData(row.id);
    },
    // 删除单条订单
    delOrder(row, data) {
      if (row.is_del === 1) {
        this.$modalSure(data)
          .then((res) => {
            this.$message.success(res.msg);
            this.getList();
          })
          .catch((res) => {
            this.$message.error(res.msg);
          });
      } else {
        this.$message.error('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！');
      }
    },
    // 获取编辑表单数据
    getOrderData(id) {
      getOrdeDatas(id)
        .then(async (res) => {
          if (res.data.status === false) {
            return this.$authLapse(res.data);
          }
          this.$authLapse(res.data);
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 获取详情表单数据
    getData(id) {
      getIntegralOrderDataInfo(id)
        .then(async (res) => {
          this.$refs.detailss.modals = true;
          this.orderDatalist = res.data;
          if (this.orderDatalist.orderInfo.refund_reason_wap_img) {
            try {
              this.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(
                this.orderDatalist.orderInfo.refund_reason_wap_img,
              );
            } catch (e) {
              this.orderDatalist.orderInfo.refund_reason_wap_img = [];
            }
          }
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 修改成功
    submitFail() {
      this.$emit('updata');
      this.getList();
    },
    // 发送货
    sendOrder(row) {
      this.$refs.send.modals = true;
      this.$refs.send.getList();
      this.$refs.send.getDeliveryList();
      // this.$refs.send.getSheetInfo();
      this.orderId = row.id;
    },
    // 配送信息表单数据
    delivery(row) {
      getIntegralOrderDistribution(row.id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 生成统计数据
    generateStats(orderList) {
      if (!orderList || orderList.length === 0) {
        return {
          total_orders: 0,
          total_integral: 0,
          pending_delivery: 0,
          completed_orders: 0
        };
      }
      
      const stats = {
        total_orders: orderList.length,
        total_integral: 0,
        pending_delivery: 0,
        completed_orders: 0
      };
      
      orderList.forEach(order => {
        // 统计积分数
        stats.total_integral += (order.integral || order.total_price || 0);
        
        // 统计各状态订单数
        const status = order.orderStatus !== undefined ? order.orderStatus : order.status;
        if (status === 1) {
          stats.pending_delivery++;
        } else if (status === 3) {
          stats.completed_orders++;
        }
      });
      
      return stats;
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消',
        '-1': '已退款'
      };
      return statusMap[status] || '未知状态';
    },
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        0: 'warning',
        1: 'primary', 
        2: 'info',
        3: 'success',
        4: 'danger',
        '-1': 'danger'
      };
      return colorMap[status] || 'info';
    },
    // 数据导出；
    exportData: function () {
      this.$refs.table.exportCsv({
        filename: '商品列表',
      });
    },
    onSelectCancel(selection, row) {},
  },
};
</script>

<style lang="scss" scoped>
.operation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-button {
    margin: 0;
  }
}

img {
  height: 36px;
  display: block;
}
.tabBox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  
  .tabBox_img {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #eee;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .tabBox_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-height: 60px;
    justify-content: space-between;
    
    .tabBox_tit {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .tabBox_spec {
      font-size: 12px;
      color: #999;
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 2px;
      align-self: flex-start;
    }
    
    .tabBox_pice {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .integral-badge {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .quantity {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
.orderData ::v-deep .ivu-table-cell {
  padding-left: 0 !important;
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}
.orderData .ivu-table {
  overflow: visible !important;
}
.orderData .ivu-table th {
  overflow: visible !important;
}
.orderData .ivu-table-header {
  overflow: visible !important;
}
::v-deep .ivu-table-header {
}
::v-deep .ivu-table th {
  overflow: visible;
}
::v-deep .select-item:hover {
  background-color: #f3f3f3;
}
::v-deep .select-on {
  display: block;
}
::v-deep .select-item.on {
  background: #f3f3f3;
}
</style>
