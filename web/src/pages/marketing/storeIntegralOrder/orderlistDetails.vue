<template>
  <div>
    <!-- 统计卡片 -->
    <el-row :gutter="16" class="mb16" v-if="cardLists && Object.keys(cardLists).length > 0">
      <el-col :span="6" v-for="(card, index) in statisticsCards" :key="index">
        <el-card shadow="hover" class="statistics-card">
          <div class="card-content">
            <div class="card-icon">
              <i :class="card.icon" :style="{color: card.color}"></i>
            </div>
            <div class="card-info">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">{{ card.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <table-form
          ref="orderData"
          :is-all="isAll"
          :auto-disabled="autoDisabled"
          :form-selection="selection"
          @getList="getData"
          @order-data="orderDatas"
          @order-chart="getCards"
        />
      </div>
    </el-card>
    <el-card :bordered="false" shadow="never" class="mt16">
      <table-list
        ref="table"
        :where="orderData"
        :is-all="isAll"
        @on-all="onAll"
        @auto-disabled="onAutoDisabled"
        @order-data="onOrderData"
        @on-changeCards="getCards"
        @changeGetTabs="changeGetTabs"
        @order-select="orderSelect"
        @updata="updata"
      />
    </el-card>
  </div>
</template>

<script>
import cardsData from '../../../components/cards/cards';
import tableForm from './components/tableFrom';
import tableList from './components/tableList';
export default {
  name: 'orderlistDetails',
  components: {
    tableForm,
    tableList,
    cardsData,
  },
  data() {
    return {
      currentTab: '',
      cardLists: {},
      selection: [],
      orderData: {
        status: '',
        data: '',
        real_name: '',
        field_key: 'all',
        pay_type: '',
        min_integral: null,
        max_integral: null,
      },
      autoDisabled: true,
      isAll: -1,
    };
  },
  computed: {
    // 统计卡片数据
    statisticsCards() {
      if (!this.cardLists || Object.keys(this.cardLists).length === 0) {
        return [];
      }
      return [
        {
          title: '总订单数',
          value: this.cardLists.total_orders || 0,
          icon: 'el-icon-document',
          color: '#409EFF'
        },
        {
          title: '总积分数',
          value: this.cardLists.total_integral || 0,
          icon: 'el-icon-coin',
          color: '#67C23A'
        },
        {
          title: '待发货订单',
          value: this.cardLists.pending_delivery || 0,
          icon: 'el-icon-truck',
          color: '#E6A23C'
        },
        {
          title: '已完成订单',
          value: this.cardLists.completed_orders || 0,
          icon: 'el-icon-check',
          color: '#67C23A'
        }
      ];
    }
  },
  methods: {
    updata() {
      this.$refs.orderData.integralGetOrdes();
    },
    changeGetTabs() {
      this.$parent.getTabs();
    },
    // tab xuanxiang dezhi
    getChangeTabs(tab) {
      this.$refs.table.getList();
    },
    // 列表数据
    getData(res) {
      if (this.$refs.table) {
        this.$refs.table.checkBox = false;
        this.$refs.table.getList(res);
      }
    },
    // 模块数据
    getCards(list) {
      console.log('接收到统计数据:', list);
      this.cardLists = list || {};
    },
    handleResize() {
      this.$refs.ellipsis.forEach((item) => item.init());
    },
    orderSelect(selection) {
      this.selection = selection;
    },
    onOrderData(e) {
      this.orderData = e;
    },
    orderDatas(e) {
      this.orderData = e;
    },
    onAutoDisabled(e) {
      this.autoDisabled = e ? true : false;
    },
    onAll(e) {
      this.isAll = e;
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.mb16 {
  margin-bottom: 16px;
}

.statistics-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .card-content {
    display: flex;
    align-items: center;
    padding: 10px;
    
    .card-icon {
      font-size: 32px;
      margin-right: 16px;
      
      i {
        font-size: inherit;
      }
    }
    
    .card-info {
      flex: 1;
      
      .card-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

.card_cent ::v-deep .ivu-card-body {
  width: 100%;
  height: 100%;
}
.card_box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 25px;
  box-sizing: border-box;
  border-radius: 4px;
  .card_box_img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;

    img {
      width: 100%;
      height: 100%;
    }
  }
  .card_box_txt {
    .sp1 {
      display: block;
      color: #252631;
      font-size: 24px;
    }
    .sp2 {
      display: block;
      color: #98a9bc;
      font-size: 12px;
    }
  }
}
</style>
