<template>
  <div v-loading="spinShow">
    <el-card :bordered="false" shadow="never" class="ivu-mb-16">
      <div class="acea-row row-middle row-between">
        <div class="acea-row row-middle">
          <span class="label_text">时间选择：</span>
          <el-date-picker clearable v-model="timeVal" type="daterange" :editable="false" @change="onchangeTime"
            format="yyyy/MM/dd" value-format="yyyy/MM/dd" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="pickerOptions" style="width: 250px" class="mr20"></el-date-picker>
          <el-button-group class="mr20">
            <el-button size="small" @click="setQuickTime('today')"
              :type="quickTimeType === 'today' ? 'primary' : ''">今日</el-button>
            <el-button size="small" @click="setQuickTime('yesterday')"
              :type="quickTimeType === 'yesterday' ? 'primary' : ''">昨日</el-button>
            <el-button size="small" @click="setQuickTime('week')"
              :type="quickTimeType === 'week' ? 'primary' : ''">近7天</el-button>
            <el-button size="small" @click="setQuickTime('month')"
              :type="quickTimeType === 'month' ? 'primary' : ''">近30天</el-button>
            <el-button size="small" @click="setQuickTime('quarter')"
              :type="quickTimeType === 'quarter' ? 'primary' : ''">近90天</el-button>
          </el-button-group>
        </div>
        <div class="acea-row row-middle">
          <el-switch v-model="autoRefresh" @change="toggleAutoRefresh" active-text="自动刷新" inactive-text="" class="mr20">
          </el-switch>
          <el-button type="primary" size="small" @click="refreshData" :loading="refreshing">
            <i class="el-icon-refresh"></i> 刷新数据
          </el-button>
          <el-button type="success" size="small" @click="exportData" class="ml10">
            <i class="el-icon-download"></i> 导出报表
          </el-button>
        </div>
      </div>
    </el-card>
    <cards-data :cardLists="cardLists" v-if="cardLists.length >= 0"></cards-data>

    <!-- 新增实时数据卡片 -->
    <el-row :gutter="16" class="ivu-mb-16">
      <el-col :span="6">
        <el-card class="realtime-card">
          <div class="realtime-header">
            <span class="realtime-title">实时订单数</span>
            <i class="el-icon-time realtime-icon"></i>
          </div>
          <div class="realtime-value">{{ realtimeData.orderCount }}</div>
          <div class="realtime-desc">较昨日同期 {{ realtimeData.orderGrowth > 0 ? '+' : '' }}{{ realtimeData.orderGrowth }}%
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="realtime-card">
          <div class="realtime-header">
            <span class="realtime-title">实时销售额</span>
            <i class="el-icon-money realtime-icon"></i>
          </div>
          <div class="realtime-value">¥{{ realtimeData.salesAmount }}</div>
          <div class="realtime-desc">较昨日同期 {{ realtimeData.salesGrowth > 0 ? '+' : '' }}{{ realtimeData.salesGrowth }}%
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="realtime-card">
          <div class="realtime-header">
            <span class="realtime-title">在线用户数</span>
            <i class="el-icon-user realtime-icon"></i>
          </div>
          <div class="realtime-value">{{ realtimeData.onlineUsers }}</div>
          <div class="realtime-desc">活跃用户占比 {{ realtimeData.activeRate }}%</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="realtime-card">
          <div class="realtime-header">
            <span class="realtime-title">转化率</span>
            <i class="el-icon-pie-chart realtime-icon"></i>
          </div>
          <div class="realtime-value">{{ realtimeData.conversionRate }}%</div>
          <div class="realtime-desc">较昨日同期 {{ realtimeData.conversionGrowth > 0 ? '+' : '' }}{{
            realtimeData.conversionGrowth }}%</div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="ivu-mb-16" :bordered="false" shadow="never">
      <div class="acea-row row-between-wrapper">
        <h4 class="statics-header-title">营业趋势</h4>
        <div class="chart-controls">
          <el-radio-group v-model="trendType" size="small" @change="onTrendTypeChange">
            <el-radio-button label="amount">金额</el-radio-button>
            <el-radio-button label="count">数量</el-radio-button>
            <el-radio-button label="both">综合</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <echarts-new :option-data="optionData" :styles="style" height="100%" width="100%" v-if="optionData"></echarts-new>
    </el-card>

    <div class="code-row-bg">
      <el-card :bordered="false" shadow="never" class="ivu-mt">
        <div class="acea-row row-between-wrapper">
          <h4 class="statics-header-title">订单类型分析</h4>
          <div class="chart-controls">
            <div class="change-style" v-db-click @click="echartRight = !echartRight">切换样式</div>
            <el-button size="mini" type="text" @click="exportTypeData">导出</el-button>
          </div>
        </div>
        <div class="ech-box">
          <echarts-from v-if="echartRight" ref="visitChart" :infoList="infoList2" echartsTitle="circle"></echarts-from>
          <el-table v-show="!echartRight" ref="selection" :data="tabList2" v-loading="loading2" empty-text="暂无数据"
            highlight-current-row>
            <el-table-column type="index" label="序号" width="50"> </el-table-column>
            <el-table-column label="类型" min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="订单数" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.count || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="金额" min-width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column label="占比率" min-width="130">
              <template slot-scope="scope">
                <div class="percent-box">
                  <div class="line">
                    <div class="bg"></div>
                    <div class="percent" :style="'width:' + scope.row.percent + '%;'"></div>
                  </div>
                  <div class="num">{{ scope.row.percent }}%</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="平均客单价" min-width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.avgOrderValue || 0 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 新增订单状态分析 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt">
      <div class="acea-row row-between-wrapper">
        <h4 class="statics-header-title">订单状态分析</h4>
        <div class="chart-controls">
          <el-radio-group v-model="statusViewType" size="small" @change="onStatusViewChange">
            <el-radio-button label="count">数量</el-radio-button>
            <el-radio-button label="amount">金额</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="status-analysis">
        <el-row :gutter="16">
          <el-col :span="8" v-for="(status, index) in orderStatusData" :key="index">
            <div class="status-item">
              <div class="status-header">
                <span class="status-name">{{ status.name }}</span>
                <i :class="status.icon" :style="{ color: status.color }"></i>
              </div>
              <div class="status-value" :style="{ color: status.color }">
                {{ statusViewType === 'count' ? status.count : '¥' + status.amount }}
              </div>
              <div class="status-desc">
                占比 {{ status.percent }}%
                <span :class="status.trend > 0 ? 'trend-up' : 'trend-down'">
                  {{ status.trend > 0 ? '↑' : '↓' }} {{ Math.abs(status.trend) }}%
                </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 新增热销商品排行 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt">
      <div class="acea-row row-between-wrapper">
        <h4 class="statics-header-title">热销商品排行</h4>
        <div class="chart-controls">
          <el-button size="mini" type="text" @click="viewMoreProducts">查看更多</el-button>
        </div>
      </div>
      <el-table :data="hotProductsData" v-loading="loadingProducts" empty-text="暂无数据" highlight-current-row
        class="hot-products-table">
        <el-table-column type="index" label="排名" width="60">
          <template slot-scope="scope">
            <div class="rank-badge" :class="'rank-' + (scope.$index + 1)">
              {{ scope.$index + 1 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品" min-width="200">
          <template slot-scope="scope">
            <div class="product-info">
              <img :src="scope.row.image" alt="" class="product-image">
              <div class="product-details">
                <div class="product-name">{{ scope.row.name }}</div>
                <div class="product-sku">SKU: {{ scope.row.sku }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="销量" min-width="80">
          <template slot-scope="scope">
            <span class="sales-count">{{ scope.row.sales }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售额" min-width="100">
          <template slot-scope="scope">
            <span class="sales-amount">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="库存" min-width="80">
          <template slot-scope="scope">
            <span :class="scope.row.stock < 10 ? 'low-stock' : ''">{{ scope.row.stock }}</span>
          </template>
        </el-table-column>
        <el-table-column label="增长率" min-width="80">
          <template slot-scope="scope">
            <span :class="scope.row.growth > 0 ? 'growth-up' : 'growth-down'">
              {{ scope.row.growth > 0 ? '+' : '' }}{{ scope.row.growth }}%
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import cardsData from '@/components/cards/cards';
import echartsNew from '@/components/echartsNew/index';
import { getBasic, getTrend, getChannel, getType, getRealtimeData, getOrderStatus, getHotProducts } from '@/api/statistic';
import { formatDate } from '@/utils/validate';
import echartsFrom from '@/components/echarts/index';
import timeOptions from '@/libs/timeOptions';

export default {
  name: 'index',
  components: { cardsData, echartsNew, echartsFrom },
  data() {
    return {
      timeVal: [],
      style: { height: '400px' },
      infoList: {},
      infoList2: {},
      echartLeft: true,
      echartRight: false,
      loading: false,
      loading2: false,
      loadingProducts: false,
      refreshing: false,
      autoRefresh: false,
      refreshTimer: null,
      quickTimeType: 'week',
      trendType: 'both',
      statusViewType: 'count',
      formValidate: {
        data: '', // 修改为date参数名
      },
      cardLists: [
        {
          col: 6,
          count: 0,
          name: '订单量',
          className: 'icondingdanliang',
        },
        {
          col: 6,
          count: 0,
          name: '订单销售额',
          className: 'icondingdanjine',
        },
        {
          col: 6,
          count: 0,
          name: '退款订单数',
          className: 'icontuikuandingdanliang',
        },
        {
          col: 6,
          count: 0,
          name: '退款金额',
          className: 'icontuikuanjine',
        },
      ],
      optionData: {},
      spinShow: false,
      tabList: [],
      tabList2: [],
      pickerOptions: timeOptions,
      // 实时数据
      realtimeData: {
        orderCount: 0,
        orderGrowth: 0,
        salesAmount: 0,
        salesGrowth: 0,
        onlineUsers: 0,
        activeRate: 0,
        conversionRate: 0,
        conversionGrowth: 0,
      },
      // 订单状态数据
      orderStatusData: [
        {
          name: '待支付',
          icon: 'el-icon-time',
          color: '#E6A23C',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
        {
          name: '待发货',
          icon: 'el-icon-box',
          color: '#409EFF',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
        {
          name: '待收货',
          icon: 'el-icon-truck',
          color: '#67C23A',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
        {
          name: '已完成',
          icon: 'el-icon-check',
          color: '#67C23A',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
        {
          name: '已取消',
          icon: 'el-icon-close',
          color: '#F56C6C',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
        {
          name: '退款中',
          icon: 'el-icon-refresh-left',
          color: '#909399',
          count: 0,
          amount: 0,
          percent: 0,
          trend: 0,
        },
      ],
      // 热销商品数据
      hotProductsData: [],
    };
  },
  created() {
    // 初始化为近7天
    this.setQuickTime('week');
  },
  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },
  methods: {
    onInit() {
      this.getBasic();
      this.getTrend();
      this.getChannel();
      this.getType();
    },
    // 设置快捷时间
    setQuickTime(type) {
      this.quickTimeType = type;
      const end = new Date();
      let start = new Date();

      switch (type) {
        case 'today':
          start = new Date();
          end.setHours(23, 59, 59, 999); // 设置为今天的结束时间
          start.setHours(0, 0, 0, 0); // 设置为今天的开始时间
          break;
        case 'yesterday':
          start = new Date();
          start.setDate(start.getDate() - 1);
          start.setHours(0, 0, 0, 0);
          end.setDate(end.getDate() - 1);
          end.setHours(23, 59, 59, 999);
          break;
        case 'week':
          start.setDate(start.getDate() - 6); // 近7天（包括今天）
          start.setHours(0, 0, 0, 0);
          end.setHours(23, 59, 59, 999);
          break;
        case 'month':
          start.setDate(start.getDate() - 29); // 近30天（包括今天）
          start.setHours(0, 0, 0, 0);
          end.setHours(23, 59, 59, 999);
          break;
        case 'quarter':
          start.setDate(start.getDate() - 89); // 近90天（包括今天）
          start.setHours(0, 0, 0, 0);
          end.setHours(23, 59, 59, 999);
          break;
      }

      this.timeVal = [start, end];
      this.formValidate.data = formatDate(start, 'yyyy/MM/dd') + '-' + formatDate(end, 'yyyy/MM/dd');

      console.log('时间设置:', {
        type,
        timeRange: this.formValidate.data,
        startDate: start,
        endDate: end
      });

      // 重新加载所有数据
      this.onInit();
      this.loadRealtimeData();
      this.loadOrderStatusData();
      this.loadHotProductsData();
    },
    // 切换自动刷新
    toggleAutoRefresh(value) {
      if (value) {
        this.refreshTimer = setInterval(() => {
          this.loadRealtimeData();
        }, 30000); // 30秒刷新一次
      } else {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer);
          this.refreshTimer = null;
        }
      }
    },
    // 刷新数据
    refreshData() {
      this.refreshing = true;

      console.log('刷新数据，当前时间参数:', this.formValidate);

      // 确保使用当前选择的时间范围刷新所有数据
      Promise.all([
        this.getBasic(),
        this.getTrend(),
        this.getChannel(),
        this.getType(),
        this.loadRealtimeData(),
        this.loadOrderStatusData(),
        this.loadHotProductsData()
      ]).finally(() => {
        this.refreshing = false;
        this.$message.success('数据刷新成功');
      });
    },
    // 导出数据
    exportData() {
      this.$message.success('正在生成报表，请稍后...');
      // 这里可以调用导出API
    },
    // 趋势类型变化
    onTrendTypeChange() {
      console.log('趋势类型变化，重新获取趋势数据');
      this.getTrend();
    },
    // 状态视图变化
    onStatusViewChange() {
      // 重新计算状态数据显示
    },
    // 导出渠道数据
    exportChannelData() {
      this.$message.success('正在导出渠道数据...');
    },
    // 导出类型数据
    exportTypeData() {
      this.$message.success('正在导出类型数据...');
    },
    // 查看更多商品
    viewMoreProducts() {
      this.$router.push('/product/productList');
    },

    timeG(dd) {
      var d = new Date(dd);
      var datetime =
        d.getFullYear() +
        '-' +
        (d.getMonth() + 1) +
        '-' +
        d.getDate() +
        ' ' +
        d.getHours() +
        ':' +
        d.getMinutes() +
        ':' +
        d.getSeconds();
      return datetime;
    },
    getBasic() {
      console.log('调用getBasic，时间参数:', this.formValidate);
      getBasic(this.formValidate).then((res) => {
        let arr = ['pay_count', 'pay_price', 'refund_count', 'refund_price'];
        this.cardLists.map((i, index) => {
          i.count = res.data[arr[index]];
        });
        console.log('基础数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取基础数据失败:', error);
        this.$message.error('获取基础数据失败');
      });
    },

    getType() {
      this.loading2 = true;
      console.log('调用getType，时间参数:', this.formValidate);
      getType(this.formValidate).then((res) => {
        this.infoList2 = res.data;
        this.tabList2 = res.data.list;
        this.loading2 = false;
        console.log('类型数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取类型数据失败:', error);
        this.loading2 = false;
        this.$message.error('获取类型数据失败');
      });
    },


    getChannel() {
      this.loading = true;
      console.log('调用getChannel，时间参数:', this.formValidate);
      getChannel(this.formValidate).then((res) => {
        this.infoList = res.data;
        this.tabList = res.data.list;
        this.loading = false;
        console.log('渠道数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取渠道数据失败:', error);
        this.loading = false;
        this.$message.error('获取渠道数据失败');
      });
    },

    selectChange(e) { },
    // 具体日期变更
    onchangeTime(e) {
      this.timeVal = e;
      if (this.timeVal && this.timeVal.length === 2) {
        this.formValidate.data = formatDate(this.timeVal[0], 'yyyy/MM/dd') + '-' + formatDate(this.timeVal[1], 'yyyy/MM/dd');

        // 检查是否匹配快捷时间选项，如果不匹配则重置quickTimeType
        this.checkAndUpdateQuickTimeType();
      } else {
        this.formValidate.data = '';
        this.quickTimeType = ''; // 清空快捷时间类型
      }

      console.log('手动选择时间:', {
        timeVal: this.timeVal,
        formattedTime: this.formValidate.data,
        quickTimeType: this.quickTimeType
      });

      // 重新加载所有数据
      this.getBasic();
      this.getTrend();
      this.getChannel();
      this.getType();
      this.loadRealtimeData();
      this.loadOrderStatusData();
      this.loadHotProductsData();
    },

    // 检查并更新快捷时间类型
    checkAndUpdateQuickTimeType() {
      if (!this.timeVal || this.timeVal.length !== 2) {
        this.quickTimeType = '';
        return;
      }

      const startDate = new Date(this.timeVal[0]);
      const endDate = new Date(this.timeVal[1]);
      const today = new Date();

      // 计算天数差
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

      // 检查是否是今天
      const isToday = startDate.toDateString() === today.toDateString() &&
        endDate.toDateString() === today.toDateString();

      // 检查是否是昨天
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const isYesterday = startDate.toDateString() === yesterday.toDateString() &&
        endDate.toDateString() === yesterday.toDateString();

      // 检查是否是近期时间范围（结束时间是今天）
      const isEndToday = endDate.toDateString() === today.toDateString();

      if (isToday) {
        this.quickTimeType = 'today';
      } else if (isYesterday) {
        this.quickTimeType = 'yesterday';
      } else if (isEndToday && daysDiff === 7) {
        this.quickTimeType = 'week';
      } else if (isEndToday && daysDiff === 30) {
        this.quickTimeType = 'month';
      } else if (isEndToday && daysDiff === 90) {
        this.quickTimeType = 'quarter';
      } else {
        this.quickTimeType = 'custom'; // 自定义时间范围
      }

      console.log('时间范围检查结果:', {
        daysDiff,
        isEndToday,
        quickTimeType: this.quickTimeType
      });
    },
    // 统计图
    getTrend() {
      this.spinShow = true;
      console.log('调用getTrend，时间参数:', this.formValidate);
      getTrend(this.formValidate)
        .then(async (res) => {
          console.log('趋势数据获取成功:', res.data);
          let legend = res.data.series.map((item) => {
            return item.name;
          });
          let xAxis = res.data.xAxis;
          let col = ['#5B8FF9', '#5AD8A6', '#FFAB2B', '#5D7092'];
          let series = [];
          res.data.series.map((item, index) => {
            series.push({
              name: item.name,
              type: 'line',
              data: item.data,
              itemStyle: {
                normal: {
                  color: col[index],
                },
              },
              smooth: 0,
            });
          });
          this.optionData = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                label: {
                  backgroundColor: '#6a7985',
                },
              },
            },
            legend: {
              x: 'center',
              data: legend,
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true,
            },
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              axisLabel: {
                interval: 0,
                rotate: 40,
                textStyle: {
                  color: '#000000',
                },
              },
              data: xAxis,
            },
            yAxis: {
              type: 'value',
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#7F8B9C',
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#F5F7F9',
                },
              },
            },
            series: series,
          };
          this.spinShow = false;
        })
        .catch((res) => {
          console.error('获取趋势数据失败:', res);
          this.$message.error(res.msg || '获取趋势数据失败');
          this.spinShow = false;
        });
    },

    // 加载实时数据
    loadRealtimeData() {
      console.log('调用loadRealtimeData，时间参数:', this.formValidate);
      return getRealtimeData(this.formValidate).then((res) => {
        this.realtimeData = {
          orderCount: res.data.orderCount || 0,
          orderGrowth: res.data.orderGrowth || 0,
          salesAmount: res.data.salesAmount || 0,
          salesGrowth: res.data.salesGrowth || 0,
          onlineUsers: res.data.onlineUsers || 0,
          activeRate: res.data.activeRate || 0,
          conversionRate: res.data.conversionRate || 0,
          conversionGrowth: res.data.conversionGrowth || 0,
        };
        console.log('实时数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取实时数据失败:', error);
        this.$message.error('获取实时数据失败，请稍后重试');
        // 初始化为空数据
        this.realtimeData = {
          orderCount: 0,
          orderGrowth: 0,
          salesAmount: 0,
          salesGrowth: 0,
          onlineUsers: 0,
          activeRate: 0,
          conversionRate: 0,
          conversionGrowth: 0,
        };
      });
    },
    // 加载订单状态数据
    loadOrderStatusData() {
      console.log('调用loadOrderStatusData，时间参数:', this.formValidate);
      return getOrderStatus(this.formValidate).then((res) => {
        if (res.data && res.data.data) {
          this.orderStatusData = res.data.data;
        }
        console.log('订单状态数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取订单状态数据失败:', error);
        this.$message.error('获取订单状态数据失败，请稍后重试');
        // 初始化为空数据
        this.orderStatusData.forEach(status => {
          status.count = 0;
          status.amount = 0;
          status.percent = 0;
          status.trend = 0;
        });
      });
    },
    // 加载热销商品数据
    loadHotProductsData() {
      this.loadingProducts = true;
      console.log('调用loadHotProductsData，时间参数:', this.formValidate);
      return getHotProducts({
        data: this.formValidate.data,
        limit: 10
      }).then((res) => {
        if (res.data && res.data.data) {
          this.hotProductsData = res.data.data;
        }
        this.loadingProducts = false;
        console.log('热销商品数据获取成功:', res.data);
      }).catch((error) => {
        console.error('获取热销商品数据失败:', error);
        this.$message.error('获取热销商品数据失败，请稍后重试');
        // 初始化为空数据
        this.hotProductsData = [];
        this.loadingProducts = false;
      });
    },
  },
};
</script>

<style scoped>
.cl {
  margin-right: 20px;
}

.ech-box {
  margin-top: 10px;
}

.change-style {
  border: 1px solid #ccc;
  border-radius: 15px;
  padding: 0px 10px;
  cursor: pointer;
}

.percent-box {
  display: flex;
  align-items: center;
  padding-right: 10px;
}

.line {
  width: 100%;
  position: relative;
}

.bg {
  position: absolute;
  width: 100%;
  height: 8px;
  border-radius: 8px;
  background-color: #f2f2f2;
}

.percent {
  position: absolute;
  border-radius: 5px;
  height: 8px;
  background-color: var(--prev-color-primary);
  z-index: 999;
}

.num {
  white-space: nowrap;
  margin: 0 10px;
  width: 20px;
}

/* 实时数据卡片样式 */
.realtime-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.realtime-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.realtime-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.realtime-icon {
  font-size: 18px;
  color: #409EFF;
}

.realtime-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.realtime-desc {
  font-size: 12px;
  color: #999;
}

/* 订单状态分析样式 */
.status-analysis {
  margin-top: 20px;
}

.status-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  margin-bottom: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-name {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.status-desc {
  font-size: 12px;
  color: #999;
}

.trend-up {
  color: #67C23A;
  margin-left: 8px;
}

.trend-down {
  color: #F56C6C;
  margin-left: 8px;
}

/* 热销商品表格样式 */
.hot-products-table {
  margin-top: 20px;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.rank-1 {
  background: #FFD700;
}

.rank-2 {
  background: #C0C0C0;
}

.rank-3 {
  background: #CD7F32;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: #909399;
}

.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.product-sku {
  font-size: 12px;
  color: #999;
}

.sales-count {
  color: #409EFF;
  font-weight: 500;
}

.sales-amount {
  color: #67C23A;
  font-weight: 500;
}

.low-stock {
  color: #F56C6C;
  font-weight: 500;
}

.growth-up {
  color: #67C23A;
}

.growth-down {
  color: #F56C6C;
}

/* 图表控制样式 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>>
