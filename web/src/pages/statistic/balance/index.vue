<template>
  <div v-loading="spinShow">
    <cards-data :cardLists="cardLists" v-if="cardLists.length >= 0"></cards-data>
    <el-card :bordered="false" shadow="never" class="ivu-mb-16">
      <div class="acea-row row-middle">
        <span class="label_text">时间选择：</span>
        <el-date-picker
          clearable
          v-model="timeVal"
          type="daterange"
          :editable="false"
          @change="onchangeTime"
          format="yyyy/MM/dd"
          value-format="yyyy/MM/dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          style="width: 250px"
          class="mr20"
        ></el-date-picker>
        <el-button type="primary" @click="refreshData" :loading="refreshing">刷新数据</el-button>
      </div>
    </el-card>
    <el-card class="ivu-mb-16" :bordered="false" shadow="never">
      <h3>余额使用趋势</h3>
      <echarts-new :option-data="optionData" :styles="style" height="100%" width="100%" v-if="optionData && optionData.series"></echarts-new>
      <div v-else class="no-data-placeholder">
        <i class="el-icon-pie-chart"></i>
        <p>暂无趋势数据</p>
      </div>
    </el-card>
    <div class="code-row-bg">
      <el-card :bordered="false" shadow="never" class="ivu-mt mr8">
        <div class="acea-row row-between-wrapper">
          <h3 class="statics-header-title">余额来源分析</h3>
        </div>
        <div class="ech-box">
          <el-table
            ref="selection"
            :data="tabList"
            v-loading="loading"
            empty-text="暂无数据"
            highlight-current-row
          >
            <el-table-column type="index" label="序号" width="50"> </el-table-column>
            <el-table-column label="来源" min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="金额" min-width="130">
              <template slot-scope="scope">
                <span>￥{{ formatMoney(scope.row.value) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="占比率" min-width="130">
              <template slot-scope="scope">
                <div class="percent-box">
                  <div class="line">
                    <div class="bg"></div>
                    <div class="percent" :style="'width:' + scope.row.percent + '%'"></div>
                  </div>
                  <div class="num">{{ scope.row.percent }}%</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card :bordered="false" shadow="never" class="ivu-mt ml8 pr10">
        <div class="acea-row row-between-wrapper">
          <h3 class="statics-header-title">余额消耗</h3>
        </div>
        <div class="ech-box">
          <el-table
            ref="selection2"
            :data="tabList2"
            v-loading="loading2"
            empty-text="暂无数据"
            highlight-current-row
          >
            <el-table-column type="index" label="序号" width="50"> </el-table-column>
            <el-table-column label="来源" min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="金额" min-width="130">
              <template slot-scope="scope">
                <span>￥{{ formatMoney(scope.row.value) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="占比率" min-width="130">
              <template slot-scope="scope">
                <div class="percent-box">
                  <div class="line">
                    <div class="bg"></div>
                    <div class="percent" :style="'width:' + scope.row.percent + '%'"></div>
                  </div>
                  <div class="num">{{ scope.row.percent }}%</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import cardsData from '@/components/cards/cards';
import echartsNew from '@/components/echartsNew/index';
import { getBalanceBasic, getBalanceTrend, getBalanceChannel, getBalanceType } from '@/api/statistic';
import { formatDate } from '@/utils/validate';
import echartsFrom from '@/components/echarts/index';
import dateRadio from '@/components/dateRadio';

export default {
  name: 'index',
  components: { cardsData, echartsNew, echartsFrom, dateRadio },
  data() {
    return {
      timeVal: [],
      style: { height: '400px' },
      infoList: {},
      infoList2: {},
      loading: false,
      loading2: false,
      refreshing: false,
      pickerOptions: this.$timeOptions,
      formValidate: {
        time: '',
      },
      cardLists: [
        {
          col: 8,
          count: 0,
          name: '当前余额',
          className: 'iconyuexiaohaojine',
        },
        {
          col: 8,
          count: 0,
          name: '累计余额',
          className: 'iconyuechongzhi',
        },
        {
          col: 8,
          count: 0,
          name: '累计消耗余额',
          className: 'iconyuexiaohaojine',
        },
      ],
      optionData: {},
      spinShow: false,
      options: this.$timeOptions,
      tabList: [],
      tabList2: [],
    };
  },
  created() {
    const end = new Date();
    const start = new Date();
    start.setTime(start.getTime() - 29 * 24 * 60 * 60 * 1000);
    this.timeVal = [start, end];
    this.formValidate.time = formatDate(start, 'yyyy/MM/dd') + '-' + formatDate(end, 'yyyy/MM/dd');
    this.onInit();
  },
  methods: {
    onInit() {
      this.getBalanceBasic();
      this.getBalanceTrend();
      this.getBalanceChannel();
      this.getBalanceType();
    },
    refreshData() {
      this.refreshing = true;
      try {
        this.onInit();
      } finally {
        setTimeout(() => {
          this.refreshing = false;
        }, 1000);
      }
    },
    onSelectDate(e) {
      this.formValidate.time = e;
      this.onInit();
    },
    formatMoney(value) {
      if (!value) return '0.00';
      const num = parseFloat(value);
      return num.toFixed(2);
    },
    getBalanceBasic() {
      getBalanceBasic(this.formValidate).then((res) => {
        if (res.success && res.data) {
          let arr = ['now_balance', 'add_balance', 'sub_balance'];
          this.cardLists.forEach((item, index) => {
            const value = res.data[arr[index]];
            item.count = value ? parseFloat(value).toFixed(2) : '0.00';
          });
        } else {
          this.$message.error(res.msg || '获取余额统计数据失败');
        }
      }).catch((error) => {
        console.error('获取余额统计数据失败:', error);
        this.$message.error('获取余额统计数据失败');
        // 设置默认值
        this.cardLists.forEach((item) => {
          item.count = '0.00';
        });
      });
    },
    getBalanceChannel() {
      this.loading = true;
      getBalanceChannel(this.formValidate).then((res) => {
        if (res.success && res.data) {
          this.infoList = res.data;
          this.tabList = res.data.list || [];
        } else {
          this.$message.error(res.msg || '获取余额来源分析数据失败');
          this.infoList = {};
          this.tabList = [];
        }
      }).catch((error) => {
        console.error('获取余额来源分析数据失败:', error);
        this.$message.error('获取余额来源分析数据失败');
        this.infoList = {};
        this.tabList = [];
      }).finally(() => {
        this.loading = false;
      });
    },
    getBalanceType() {
      this.loading2 = true;
      getBalanceType(this.formValidate).then((res) => {
        if (res.success && res.data) {
          this.infoList2 = res.data;
          this.tabList2 = res.data.list || [];
        } else {
          this.$message.error(res.msg || '获取余额消耗分析数据失败');
          this.infoList2 = {};
          this.tabList2 = [];
        }
      }).catch((error) => {
        console.error('获取余额消耗分析数据失败:', error);
        this.$message.error('获取余额消耗分析数据失败');
        this.infoList2 = {};
        this.tabList2 = [];
      }).finally(() => {
        this.loading2 = false;
      });
    },
    // 具体日期
    onchangeTime(e) {
      if (e && e.length === 2) {
        this.timeVal = e;
        this.formValidate.time = e.join('-');
      } else {
        this.timeVal = [];
        this.formValidate.time = '';
      }
      this.getBalanceBasic();
      this.getBalanceTrend();
      this.getBalanceChannel();
      this.getBalanceType();
    },
    // 统计图
    getBalanceTrend() {
      this.spinShow = true;
      getBalanceTrend(this.formValidate)
        .then(async (res) => {
          if (res.success && res.data) {
            let legend = res.data.series ? res.data.series.map((item) => {
              return item.name;
            }) : [];
            let xAxis = res.data.xAxis || [];
            let col = ['#5B8FF9', '#5AD8A6', '#FFAB2B', '#5D7092'];
            let series = [];
            
            if (res.data.series && res.data.series.length > 0) {
              res.data.series.forEach((item, index) => {
                series.push({
                  name: item.name,
                  type: 'line',
                  data: item.data || [],
                  itemStyle: {
                    normal: {
                      color: col[index % col.length],
                    },
                  },
                  smooth: true,
                });
              });
            }
            
            this.optionData = {
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  label: {
                    backgroundColor: '#6a7985',
                  },
                },
                formatter: function(params) {
                  let tooltipText = params[0].name + '<br/>';
                  params.forEach(function(item) {
                    tooltipText += item.marker + item.seriesName + ': ￥' + (item.value ? parseFloat(item.value).toFixed(2) : '0.00') + '<br/>';
                  });
                  return tooltipText;
                }
              },
              legend: {
                x: 'center',
                data: legend,
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
              },
              toolbox: {
                feature: {
                  saveAsImage: {},
                },
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                axisLabel: {
                  interval: 0,
                  rotate: 40,
                  textStyle: {
                    color: '#000000',
                  },
                },
                data: xAxis,
              },
              yAxis: {
                type: 'value',
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  textStyle: {
                    color: '#7F8B9C',
                  },
                  formatter: function(value) {
                    return '￥' + value;
                  }
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: '#F5F7F9',
                  },
                },
              },
              series: series,
            };
          } else {
            this.$message.error(res.msg || '获取余额趋势数据失败');
            this.optionData = {};
          }
        })
        .catch((error) => {
          console.error('获取余额趋势数据失败:', error);
          this.$message.error('获取余额趋势数据失败');
          this.optionData = {};
        })
        .finally(() => {
          this.spinShow = false;
        });
    },
  },
};
</script>

<style scoped>
.cl {
  margin-right: 20px;
}
.code-row-bg {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.code-row-bg .ivu-mt {
  min-width: 50%;
}
.ech-box {
  margin-top: 10px;
}
.percent-box {
  display: flex;
  align-items: center;
  padding-right: 10px;
}
.line {
  width: 100%;
  position: relative;
}
.bg {
  position: absolute;
  width: 100%;
  height: 8px;
  border-radius: 8px;
  background-color: #f2f2f2;
}
.percent {
  position: absolute;
  border-radius: 5px;
  height: 8px;
  background-color: var(--prev-color-primary);
  z-index: 999;
  transition: width 0.3s ease;
}
.num {
  white-space: nowrap;
  margin: 0 10px;
  width: 20px;
}
.no-data-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}
.no-data-placeholder i {
  font-size: 48px;
  margin-bottom: 10px;
  color: #ddd;
}
.label_text {
  font-weight: 500;
  margin-right: 8px;
}
.mr20 {
  margin-right: 20px;
}
.acea-row {
  display: flex;
  align-items: center;
}
.row-middle {
  align-items: center;
}
.row-between-wrapper {
  justify-content: space-between;
  align-items: center;
}
.statics-header-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}
.ivu-mb-16 {
  margin-bottom: 16px;
}
.ivu-mt {
  margin-top: 16px;
}
.mr8 {
  margin-right: 8px;
}
.ml8 {
  margin-left: 8px;
}
.pr10 {
  padding-right: 10px;
}
</style>
