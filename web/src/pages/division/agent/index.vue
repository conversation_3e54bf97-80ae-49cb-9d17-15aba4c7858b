<template>
  <div>
    <!-- 下钻导航提示 -->
    <el-card v-if="drillDownLevel > 0" :bordered="false" shadow="never" class="ivu-mt">
      <div class="drilldown-tip">
        <el-button type="text" @click="goBack">返回上一级</el-button>
        <el-button type="text" @click="goHome" v-if="drillDownLevel > 1">回到首页</el-button>
        <span class="tip-text">当前查看：{{ getCurrentDrillPath() }}</span>
      </div>
    </el-card>
    
    <el-card :bordered="false" shadow="never" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="formValidate"
          :model="formValidate"
          :label-width="labelWidth"
          :label-position="labelPosition"
          @submit.native.prevent
          inline
        >
          <el-form-item label="搜索：">
            <el-input
              clearable
              placeholder="请输入姓名、UID"
              v-model="formValidate.keyword"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" v-db-click @click="userSearchs">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-row class="ivu-mt box-wrapper">
        <el-col :xs="24" :sm="24" ref="rightBox">
          <el-row>
            <el-col v-bind="grid">
              <el-button type="primary" v-db-click @click="groupAdd('0')" class="mr20">添加代理商</el-button>
            </el-col>
          </el-row>
          <el-table
            :data="userLists"
            ref="table"
            class="mt14"
            v-loading="loading"
            highlight-current-row
            no-formValidate-text="暂无数据"
            no-filtered-formValidate-text="暂无筛选结果"
          >
            <el-table-column label="用户ID" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.id }}</span>
              </template>
            </el-table-column>
            <el-table-column label="头像" min-width="90">
              <template slot-scope="scope">
                <div class="tabBox_img" v-viewer>
                  <img v-lazy="scope.row.avatar" />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="昵称/用户名/手机号" min-width="150">
              <template slot-scope="scope">
                <div class="acea-row">
                  <div>
                    <div v-text="scope.row.nickname || scope.row.username || '匿名用户'" class="ml10"></div>
                    <div v-if="scope.row.mobile" style="font-size: 12px; color: #999;">{{ scope.row.mobile }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="用户等级" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.userLevelText }}</span>
              </template>
            </el-table-column>
            <el-table-column label="订单数" min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.orderCount || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消费金额" min-width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.totalAmount || '0.00' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="积分" min-width="80">
              <template slot-scope="scope">
                <span>{{ scope.row.points || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="余额" min-width="100">
              <template slot-scope="scope">
                <span>¥{{ scope.row.balance || '0.00' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总直邀" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.promotionCount || 0 }}人</span>
              </template>
            </el-table-column>
            <el-table-column label="今日直邀" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.todayInviteCount || 0 }}人</span>
              </template>
            </el-table-column>
            <el-table-column label="本月直邀" min-width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.monthInviteCount || 0 }}人</span>
              </template>
            </el-table-column>
            <el-table-column label="待确认收益" min-width="120">
              <template slot-scope="scope">
                <span>¥{{ scope.row.pendingEarnings || '0.00' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="可提现金额" min-width="120">
              <template slot-scope="scope">
                <span>¥{{ scope.row.withdrawableAmount || '0.00' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="130">
              <template slot-scope="scope">
                <el-switch
                  :active-value="1"
                  :inactive-value="0"
                  v-model="scope.row.division_status"
                  :value="scope.row.division_status"
                  @change="onchangeIsShow(scope.row)"
                  size="large"
                >
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="250">
              <template slot-scope="scope">
                <a v-db-click @click="viewPromotionDetail(scope.row)">直邀明细</a>
                <el-divider direction="vertical"></el-divider>
                <a v-db-click @click="viewUserOrders(scope.row)">查看订单</a>
                <el-divider direction="vertical"></el-divider>
                <a v-db-click @click="viewWithdrawableDetail(scope.row)">收入明细</a>
              </template>
            </el-table-column>
          </el-table>
          <div class="acea-row row-right page">
            <pagination
              v-if="total"
              :total="total"
              :page.sync="formValidate.page"
              :limit.sync="formValidate.limit"
              @pagination="handlePageChange"
            />
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-dialog :visible.sync="staffModal" title="员工列表" class="order_box" width="1000px">
      <el-table
        :data="clerkLists"
        ref="table"
        class="mt20"
        v-loading="loading"
        highlight-current-row
        no-formValidate-text="暂无数据"
        no-filtered-formValidate-text="暂无筛选结果"
      >
        <el-table-column label="用户ID" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="头像" min-width="120">
          <template slot-scope="scope">
            <div class="tabBox_img" v-viewer>
              <img v-lazy="scope.row.avatar" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="昵称/用户名/手机号" min-width="150">
          <template slot-scope="scope">
            <div class="acea-row">
              <div>
                <div v-text="scope.row.nickname || scope.row.username || '匿名用户'" class=""></div>
                <div v-if="scope.row.mobile" style="font-size: 12px; color: #999;">{{ scope.row.mobile }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="用户等级" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.userLevelText }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单数" min-width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.orderCount || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="消费金额" min-width="100">
          <template slot-scope="scope">
            <span>¥{{ scope.row.totalAmount || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总直邀" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.promotionCount || 0 }}人</span>
          </template>
        </el-table-column>
        <el-table-column label="今日直邀" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.todayInviteCount || 0 }}人</span>
          </template>
        </el-table-column>
        <el-table-column label="待确认收益" min-width="120">
          <template slot-scope="scope">
            <span>¥{{ scope.row.pendingEarnings || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可提现金额" min-width="120">
          <template slot-scope="scope">
            <span>¥{{ scope.row.withdrawableAmount || '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <a v-db-click @click="viewPromotionDetail(scope.row)">直邀明细</a>
            <el-divider direction="vertical"></el-divider>
            <a v-db-click @click="viewUserOrders(scope.row)">查看订单</a>
            <el-divider direction="vertical"></el-divider>
            <a v-db-click @click="viewWithdrawableDetail(scope.row)">收入明细</a>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
          v-if="total2"
          :total="total2"
          :page.sync="clerkReqData.page"
          :limit.sync="clerkReqData.limit"
          @pagination="getClerkList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { regionList, agentFrom, isShowApi, clerkList, staffAddFrom } from '@/api/agent';
import { getPromotionUserList, searchPromotionUsers } from '@/api/promotion';
import { formatDate } from '@/utils/validate';
export default {
  name: 'agent_extra',
  data() {
    return {
      grid: {
        xl: 7,
        lg: 7,
        md: 12,
        sm: 24,
        xs: 24,
      },
      total: 0,
      total2: 0,
      userLists: [],
      formInline: {
        uid: 0,
        proportion: 0,
        image: '',
      },
      FromData: null,
      loading: false,
      current: 0,
      formValidate: {
        page: 1,
        limit: 15,
        keyword: '',
      },
      staffModal: false,
      clerkReqData: {
        uid: 0,
        page: 1,
        limit: 15,
      },
      clerkLists: [],
      // 下钻相关状态
      drillDownLevel: 0, // 下钻层级：0-第一级（区域总监），1-第二级（直邀用户）
      currentDrillUserId: null, // 当前下钻的用户ID
      currentDrillUserName: '', // 当前下钻的用户名称
      drillDownStack: [], // 下钻历史栈
    };
  },
  filters: {
    formatDate(time) {
      if (time !== 0) {
        let date = new Date(time * 1000);
        return formatDate(date, 'yyyy-MM-dd hh:mm');
      }
    },
  },
  computed: {
    ...mapState('media', ['isMobile']),
    labelWidth() {
      return this.isMobile ? undefined : '50px';
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    userSearchs() {
      this.formValidate.page = 1;
      if (this.drillDownLevel > 0) {
        // 如果是下钻状态，加载下钻列表
        this.loadDrillDownList();
      } else {
        // 否则加载主列表
        this.getList();
      }
    },
    jump(uid) {
      this.clerkReqData.uid = uid;
      this.getClerkList();
    },
    getClerkList() {
      // 使用搜索推广用户接口获取员工列表
      const params = {
        searchKeyword: '',
        page: this.clerkReqData.page,
        size: this.clerkReqData.limit
      };
      searchPromotionUsers(params).then((res) => {
        this.clerkLists = res.data.list;
        this.total2 = res.data.total;
        this.staffModal = true;
      });
    },
    // 列表
    getList() {
      this.loading = true;
      // 使用新的推广用户列表接口，初始获取userLevel=2的用户（区域总监）
      const params = {
        pageNum: this.formValidate.page,
        pageSize: this.formValidate.limit,
        keyword: this.formValidate.keyword,
        userLevelId: 2 // 区域总监
      };
      getPromotionUserList(params)
        .then(async (res) => {
          let data = res.data;
          this.userLists = data.list;
          this.total = data.total;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg);
        });
    },
    // 添加表单
    groupAdd(id) {
      this.$modalForm(agentFrom(id))
        .then((res) => {
          this.getList();
        })
        .catch((err) => {});
    },
    //添加员工表单
    staffAdd(id) {
      this.$modalForm(staffAddFrom(id))
        .then((res) => {
          this.getList();
        })
        .catch((err) => {});
    },
    // 修改是否显示
    onchangeIsShow(row) {
      let data = {
        id: row.id,
        status: row.division_status,
      };
      isShowApi(data)
        .then(async (res) => {
          this.$message.success(res.msg);
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 编辑
    edit(row) {},
    // 查看推广明细
    viewPromotionDetail(row) {
      // 支持多层级下钻，始终在当前页面处理
      this.drillDownToInvitedUsers(row);
    },
    // 查看用户订单
    viewUserOrders(row) {
      // 跳转到订单页面
      this.$router.push({
        path: '/admin/order/list',
        query: {
          userId: row.id
        }
      });
    },
    // 查看可提现金额收入明细
    viewWithdrawableDetail(row) {
      // 跳转到收益明细页面
      this.$router.push({
        path: '/admin/finance/balance/income-details',
        query: {
          userId: row.id,
          userName: row.nickname || row.username
        }
      });
    },
    // 处理分页变化
    handlePageChange() {
      if (this.drillDownLevel > 0) {
        // 如果是下钻状态，加载下钻列表
        this.loadDrillDownList();
      } else {
        // 否则加载主列表
        this.getList();
      }
    },
    // 获取当前下钻路径显示文本
    getCurrentDrillPath() {
      if (this.drillDownStack.length > 0) {
        // 获取栈中最后一个用户的名称作为起点
        const rootUserName = this.drillDownStack[0].currentDrillUserName || '区域总监';
        return `${rootUserName} → ${this.currentDrillUserName}的直邀列表`;
      } else {
        return `${this.currentDrillUserName}的直邀列表`;
      }
    },
    // 下钻查看直邀用户列表
    drillDownToInvitedUsers(row) {
      // 保存当前状态到栈中
      const currentState = {
        userLists: [...this.userLists],
        total: this.total,
        formValidate: { ...this.formValidate },
        drillDownLevel: this.drillDownLevel,
        currentDrillUserId: this.currentDrillUserId,
        currentDrillUserName: this.currentDrillUserName
      };
      
      // 更新下钻状态
      this.drillDownStack.push(currentState);
      this.drillDownLevel = this.drillDownLevel + 1;
      this.currentDrillUserId = row.id;
      this.currentDrillUserName = row.nickname || row.username || '匿名用户';
      
      // 清空当前列表，准备加载新数据
      this.userLists = [];
      this.total = 0;
      this.formValidate.page = 1;
      
      // 加载该用户的直邀用户列表
      this.loadDrillDownList();
    },
    // 加载下钻列表
    loadDrillDownList() {
      this.loading = true;
      // 使用搜索推广用户接口获取直邀用户列表
      const params = {
        searchKeyword: this.formValidate.keyword,
        page: this.formValidate.page,
        size: this.formValidate.limit,
        userId: this.currentDrillUserId
      };
      
      searchPromotionUsers(params)
        .then(res => {
          let data = res.data;
          this.userLists = data.list;
          this.total = data.total;
          this.loading = false;
        })
        .catch(res => {
          this.loading = false;
          this.$message.error(res.msg);
        });
    },
    // 返回上一级
    goBack() {
      if (this.drillDownStack.length > 0) {
        // 从栈中恢复上一级状态
        const prevState = this.drillDownStack.pop();
        this.userLists = prevState.userLists;
        this.total = prevState.total;
        this.formValidate = prevState.formValidate;
        this.drillDownLevel = prevState.drillDownLevel;
        this.currentDrillUserName = prevState.currentDrillUserName;
        this.currentDrillUserId = prevState.currentDrillUserId;
      } else {
        // 如果没有上一级，重新加载区域总监列表
        this.drillDownLevel = 0;
        this.currentDrillUserId = null;
        this.currentDrillUserName = '';
        this.formValidate.page = 1;
        this.getList();
      }
    },
    // 回到首页（区域总监列表）
    goHome() {
      // 清空下钻状态
      this.drillDownStack = [];
      this.drillDownLevel = 0;
      this.currentDrillUserId = null;
      this.currentDrillUserName = '';
      this.formValidate.page = 1;
      // 重新加载区域总监列表
      this.getList();
    },
    // 删除
    del(row, tit, num, type) {
      let delfromData = {
        title: tit,
        method: 'DELETE',
        uid: row.id,
        url: `agent/division/del/${type}/${row.id}`,
      };
      this.$modalSure(delfromData)
        .then((res) => {
          this.$message.success(res.msg);
          if (type == 2) {
            this.userLists.splice(num, 1);
          } else {
            this.clerkLists.splice(num, 1);
          }
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.ivu-form-item {
  margin-bottom: 0;
}

.drilldown-tip {
  padding: 10px 15px;
  background-color: #f0f9ff;
  border: 1px solid #d0e8ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  .tip-text {
    margin-left: 10px;
    font-size: 14px;
    color: #606266;
  }
}
.picBox {
  display: inline-block;
  cursor: pointer;
  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
  }
  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
::v-deep .ivu-menu-vertical .ivu-menu-item-group-title {
  display: none;
}
::v-deep .ivu-menu-vertical.ivu-menu-light:after {
  display: none;
}
.left-wrapper {
  height: 904px;
  background: #fff;
  border-right: 1px solid #f2f2f2;
}
.menu-item {
  z-index: 50;
  position: relative;
  display: flex;
  justify-content: space-between;
  word-break: break-all;
  &:hover .icon-box {
    display: block;
  }
}
.icon-box {
  z-index: 3;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: none;
}
.right-menu {
  z-index: 10;
  position: absolute;
  right: -106px;
  top: -11px;
  width: auto;
  min-width: 121px;
}
.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
