<template>
  <div class="wx-shipping-container">
    <Card>
      <div slot="title">
        <Icon type="ios-paper-plane" />
        微信发货信息管理
      </div>
      <div slot="extra">
        <Button type="primary" @click="checkServiceStatus">
          <Icon type="ios-checkmark-circle" />
          检查服务状态
        </Button>
      </div>

      <!-- 服务状态提示 -->
      <Alert v-if="serviceStatus !== null" :type="serviceStatus ? 'success' : 'warning'" show-icon>
        <span slot="desc">
          {{ serviceStatus ? '微信发货信息管理服务已开通' : '微信发货信息管理服务未开通，请先在微信小程序后台开通此服务' }}
        </span>
      </Alert>

      <!-- 搜索条件 -->
      <Row :gutter="16" class="search-row">
        <Col span="6">
          <Input v-model="searchForm.orderId" placeholder="请输入订单ID" clearable />
        </Col>
        <Col span="6">
          <Input v-model="searchForm.transactionId" placeholder="请输入微信支付单号" clearable />
        </Col>
        <Col span="6">
          <Select v-model="searchForm.wxStatus" placeholder="微信上传状态" clearable>
            <Option value="0">待上传</Option>
            <Option value="1">上传成功</Option>
            <Option value="2">上传失败</Option>
          </Select>
        </Col>
        <Col span="6">
          <Button type="primary" @click="handleSearch">
            <Icon type="ios-search" />
            搜索
          </Button>
          <Button @click="handleReset" style="margin-left: 8px;">重置</Button>
        </Col>
      </Row>

      <!-- 操作按钮 -->
      <Row class="action-row">
        <Col span="24">
          <Button type="success" @click="showBatchUploadModal">
            <Icon type="ios-cloud-upload" />
            批量上传发货信息
          </Button>
          <Button type="info" @click="setJumpPath" style="margin-left: 8px;">
            <Icon type="ios-settings" />
            设置跳转路径
          </Button>
        </Col>
      </Row>

      <!-- 数据表格 -->
      <Table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        stripe
        border
        size="small"
      >
        <template slot-scope="{ row }" slot="wxStatus">
          <Tag :color="getStatusColor(row.wxStatus)">
            {{ getStatusText(row.wxStatus) }}
          </Tag>
        </template>
        
        <template slot-scope="{ row }" slot="logisticsType">
          {{ getLogisticsTypeText(row.logisticsType) }}
        </template>
        
        <template slot-scope="{ row }" slot="deliveryMode">
          {{ getDeliveryModeText(row.deliveryMode) }}
        </template>
        
        <template slot-scope="{ row }" slot="action">
          <Button 
            type="primary" 
            size="small" 
            @click="uploadSingle(row)"
            :disabled="row.wxStatus === 1"
          >
            上传
          </Button>
          <Button 
            type="info" 
            size="small" 
            @click="viewDetails(row)"
            style="margin-left: 4px;"
          >
            详情
          </Button>
          <Button 
            type="warning" 
            size="small" 
            @click="notifyReceive(row)"
            style="margin-left: 4px;"
            :disabled="row.wxStatus !== 1"
          >
            提醒收货
          </Button>
        </template>
      </Table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <Page
          :total="total"
          :current="currentPage"
          :page-size="pageSize"
          @on-change="handlePageChange"
          @on-page-size-change="handlePageSizeChange"
          show-sizer
          show-elevator
          show-total
        />
      </div>
    </Card>

    <!-- 批量上传模态框 -->
    <Modal
      v-model="batchUploadModal"
      title="批量上传发货信息"
      :mask-closable="false"
      width="600"
    >
      <Form :model="batchForm" :label-width="120">
        <FormItem label="选择订单状态:">
          <Select v-model="batchForm.orderStatus" placeholder="请选择订单状态">
            <Option value="1">待发货</Option>
            <Option value="2">待收货</Option>
          </Select>
        </FormItem>
        <FormItem label="时间范围:">
          <DatePicker
            v-model="batchForm.dateRange"
            type="daterange"
            placeholder="选择日期范围"
            style="width: 100%"
          />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="batchUploadModal = false">取消</Button>
        <Button type="primary" @click="handleBatchUpload" :loading="batchUploading">
          确定上传
        </Button>
      </div>
    </Modal>

    <!-- 详情模态框 -->
    <Modal
      v-model="detailModal"
      title="发货信息详情"
      :mask-closable="false"
      width="800"
    >
      <div v-if="currentDetail">
        <Descriptions :column="2" border>
          <DescriptionsItem label="订单ID">{{ currentDetail.orderId }}</DescriptionsItem>
          <DescriptionsItem label="微信支付单号">{{ currentDetail.transactionId }}</DescriptionsItem>
          <DescriptionsItem label="商户号">{{ currentDetail.mchid }}</DescriptionsItem>
          <DescriptionsItem label="商户订单号">{{ currentDetail.outTradeNo }}</DescriptionsItem>
          <DescriptionsItem label="用户OpenID">{{ currentDetail.openid }}</DescriptionsItem>
          <DescriptionsItem label="物流模式">{{ getLogisticsTypeText(currentDetail.logisticsType) }}</DescriptionsItem>
          <DescriptionsItem label="发货模式">{{ getDeliveryModeText(currentDetail.deliveryMode) }}</DescriptionsItem>
          <DescriptionsItem label="上传状态">
            <Tag :color="getStatusColor(currentDetail.wxStatus)">
              {{ getStatusText(currentDetail.wxStatus) }}
            </Tag>
          </DescriptionsItem>
          <DescriptionsItem label="上传时间" :span="2">{{ currentDetail.uploadTime }}</DescriptionsItem>
          <DescriptionsItem label="错误信息" :span="2" v-if="currentDetail.wxErrorMsg">
            <Alert type="error" :description="currentDetail.wxErrorMsg" />
          </DescriptionsItem>
        </Descriptions>
        
        <Divider>物流信息</Divider>
        <div v-if="currentDetail.shippingList">
          <pre>{{ formatShippingList(currentDetail.shippingList) }}</pre>
        </div>
      </div>
      <div slot="footer">
        <Button @click="detailModal = false">关闭</Button>
      </div>
    </Modal>

    <!-- 设置跳转路径模态框 -->
    <Modal
      v-model="jumpPathModal"
      title="设置消息跳转路径"
      :mask-closable="false"
      width="500"
    >
      <Form :model="jumpPathForm" :label-width="100">
        <FormItem label="跳转路径:">
          <Input 
            v-model="jumpPathForm.path" 
            placeholder="例如: pages/order/detail"
            :maxlength="200"
          />
          <div class="form-tip">
            设置用户点击发货消息时跳转到的小程序页面路径
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="jumpPathModal = false">取消</Button>
        <Button type="primary" @click="handleSetJumpPath" :loading="settingPath">
          确定设置
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { 
  getWxShippingList, 
  uploadShippingInfo, 
  batchUploadShipping,
  getOrderShippingStatus,
  notifyConfirmReceive,
  setMsgJumpPath,
  checkServiceStatus
} from '@/api/wxShipping';

export default {
  name: 'WxShipping',
  data() {
    return {
      serviceStatus: null,
      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 20,
      
      searchForm: {
        orderId: '',
        transactionId: '',
        wxStatus: ''
      },
      
      columns: [
        { title: '订单ID', key: 'orderId', width: 80 },
        { title: '微信支付单号', key: 'transactionId', width: 200 },
        { title: '商户订单号', key: 'outTradeNo', width: 150 },
        { title: '物流模式', slot: 'logisticsType', width: 100 },
        { title: '发货模式', slot: 'deliveryMode', width: 100 },
        { title: '上传状态', slot: 'wxStatus', width: 100 },
        { title: '上传时间', key: 'uploadTime', width: 150 },
        { title: '操作', slot: 'action', width: 200, fixed: 'right' }
      ],
      
      batchUploadModal: false,
      batchUploading: false,
      batchForm: {
        orderStatus: '',
        dateRange: []
      },
      
      detailModal: false,
      currentDetail: null,
      
      jumpPathModal: false,
      settingPath: false,
      jumpPathForm: {
        path: 'pages/order/detail'
      }
    };
  },
  
  created() {
    this.loadData();
    this.checkServiceStatus();
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          ...this.searchForm
        };
        
        const response = await getWxShippingList(params);
        if (response.success) {
          this.tableData = response.data.records || [];
          this.total = response.data.total || 0;
        }
      } catch (error) {
        this.$Message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 检查服务状态
    async checkServiceStatus() {
      try {
        const response = await checkServiceStatus();
        if (response.success) {
          this.serviceStatus = response.data;
        }
      } catch (error) {
        this.$Message.error('检查服务状态失败');
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.loadData();
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        orderId: '',
        transactionId: '',
        wxStatus: ''
      };
      this.currentPage = 1;
      this.loadData();
    },
    
    // 分页
    handlePageChange(page) {
      this.currentPage = page;
      this.loadData();
    },
    
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loadData();
    },
    
    // 单个上传
    async uploadSingle(row) {
      try {
        const response = await uploadShippingInfo(row.orderId);
        if (response.success) {
          this.$Message.success('发货信息上传成功');
          this.loadData();
        } else {
          this.$Message.error(response.message || '上传失败');
        }
      } catch (error) {
        this.$Message.error('上传失败');
      }
    },
    
    // 查看详情
    viewDetails(row) {
      this.currentDetail = row;
      this.detailModal = true;
    },
    
    // 提醒收货
    async notifyReceive(row) {
      try {
        const response = await notifyConfirmReceive({
          transactionId: row.transactionId,
          merchantId: row.mchid,
          merchantTradeNo: row.outTradeNo
        });
        
        if (response.success) {
          this.$Message.success('确认收货提醒发送成功');
        } else {
          this.$Message.error(response.message || '发送失败');
        }
      } catch (error) {
        this.$Message.error('发送失败');
      }
    },
    
    // 显示批量上传模态框
    showBatchUploadModal() {
      this.batchUploadModal = true;
    },
    
    // 批量上传
    async handleBatchUpload() {
      this.batchUploading = true;
      try {
        const response = await batchUploadShipping(this.batchForm);
        if (response.success) {
          this.$Message.success('批量上传成功');
          this.batchUploadModal = false;
          this.loadData();
        } else {
          this.$Message.error(response.message || '批量上传失败');
        }
      } catch (error) {
        this.$Message.error('批量上传失败');
      } finally {
        this.batchUploading = false;
      }
    },
    
    // 设置跳转路径
    setJumpPath() {
      this.jumpPathModal = true;
    },
    
    async handleSetJumpPath() {
      this.settingPath = true;
      try {
        const response = await setMsgJumpPath(this.jumpPathForm.path);
        if (response.success) {
          this.$Message.success('跳转路径设置成功');
          this.jumpPathModal = false;
        } else {
          this.$Message.error(response.message || '设置失败');
        }
      } catch (error) {
        this.$Message.error('设置失败');
      } finally {
        this.settingPath = false;
      }
    },
    
    // 辅助方法
    getStatusColor(status) {
      const colors = { 0: 'default', 1: 'success', 2: 'error' };
      return colors[status] || 'default';
    },
    
    getStatusText(status) {
      const texts = { 0: '待上传', 1: '上传成功', 2: '上传失败' };
      return texts[status] || '未知';
    },
    
    getLogisticsTypeText(type) {
      const texts = { 1: '实体物流', 2: '同城配送', 3: '虚拟商品', 4: '用户自提' };
      return texts[type] || '未知';
    },
    
    getDeliveryModeText(mode) {
      const texts = { 1: '统一发货', 2: '分拆发货' };
      return texts[mode] || '未知';
    },
    
    formatShippingList(shippingList) {
      try {
        return JSON.stringify(JSON.parse(shippingList), null, 2);
      } catch {
        return shippingList;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.wx-shipping-container {
  .search-row {
    margin-bottom: 16px;
  }
  
  .action-row {
    margin-bottom: 16px;
  }
  
  .pagination-wrapper {
    margin-top: 16px;
    text-align: right;
  }
  
  .form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
  }
}
</style>
