<template>
  <div>
    <el-tabs v-model="currentTab" @tab-click="onClickTab">
      <el-tab-pane name="null" label="全部"></el-tab-pane>
      <el-tab-pane name="0" label="待支付"></el-tab-pane>
      <el-tab-pane name="1" label="待发货"></el-tab-pane>
      <el-tab-pane name="5" label="待核销"></el-tab-pane>
      <el-tab-pane name="2" label="待收货"></el-tab-pane>
      <el-tab-pane name="3" label="已完成"></el-tab-pane>
      <el-tab-pane name="4" label="已取消"></el-tab-pane>
      <el-tab-pane name="-2" label="已退款"></el-tab-pane>
      <el-tab-pane name="-4" label="已删除"></el-tab-pane>
    </el-tabs>
    <div class="acea-row">
<!--      <el-button v-auth="['order-write']" type="primary" v-db-click @click="writeOff">订单核销</el-button>-->
<!--      <el-button v-db-click @click="batchShipmentModal = true">批量发货</el-button>-->
      <el-button 
        type="success" 
        v-db-click 
        @click="quickBatchShip"
        :disabled="!hasShippableOrders"
        icon="el-icon-truck"
      >
        快速发货
      </el-button>
      <!-- <el-upload class="mr14" :action="expressUrl" :headers="header" :on-success="upExpress">
        <el-button class="export" type="primary">批量发货</el-button>
      </el-upload> -->
      <el-button v-auth="['order-dels']" v-db-click @click="delAll">批量删除</el-button>
      <el-button v-auth="['export-storeOrder']" class="export" v-db-click @click="exportList">订单导出</el-button>
      <!-- <el-button class="export" v-db-click @click="exportDeliveryList">发货单导出</el-button> -->
    </div>
    <el-table
      :data="orderList"
      ref="table"
      v-loading="loading"
      empty-text="暂无数据"
      @select="handleSelectRow"
      @select-all="handleSelectRow"
      class="orderData mt14"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <expandRow :row="scope.row"></expandRow>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column label="订单信息" width="200">
        <template slot-scope="scope">
          <div class="order-sn">{{ scope.row.orderSN || scope.row.order_id || scope.row.id }}</div>
          <div class="order-type">{{ scope.row.orderTypeName || '普通订单' }}</div>
          <div v-if="scope.row.is_del === 1" class="order-status-tag">
            <el-tag type="danger" size="mini">用户已删除</el-tag>
          </div>
          <div v-else-if="scope.row.is_cancel === 1" class="order-status-tag">
            <el-tag type="warning" size="mini">用户已取消</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品信息" min-width="250">
        <template slot-scope="scope">
          <div class="tab" v-for="(item, i) in scope.row.goodsList" :key="i">
            <img v-lazy="fir(item.listPicUrl)" />
            <el-tooltip placement="top" :open-delay="300">
              <div slot="content">
                <div>
                  <span>商品名称：</span>
                  <span>{{ item.goodsName || '--' }}</span>
                </div>
<!--                <div>
                  <span>规格名称：</span>
                 <span>{{item.cart_info.productInfo.attrInfo ? item.cart_info.productInfo.attrInfo.suk : '-&#45;&#45;' }}</span>
                </div>-->
                <div>
                  <span>支付价格：</span>
                  <span>¥{{ item.retailPrice || '--' }}</span>
                </div>
                <div>
                  <span>购买数量：</span>
                  <span>{{ item.number || '--' }}</span>
                </div>
              </div>
              <span class="line2 w-250">{{ item.goodsName}}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="用户信息" min-width="150">
        <template slot-scope="scope">
          <span class="nickname">{{ scope.row.consignee }} | {{ scope.row.userId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" min-width="150">
        <template slot-scope="scope">
          <span class="text-primary">¥{{ scope.row.actualPrice || scope.row.pay_price || '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" min-width="100">
        <template slot-scope="scope">
          <div>
            <el-tag :type="getPayStatusType(scope.row.payStatus && scope.row.payStatus.value !== undefined ? scope.row.payStatus.value : scope.row.payStatus)" size="small">
              {{ getPayStatusText(scope.row.payStatus && scope.row.payStatus.value !== undefined ? scope.row.payStatus.value : scope.row.payStatus) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" min-width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.payTime" class="text-muted text-sm">{{ scope.row.payTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getOrderStatusType(scope.row.orderStatus && scope.row.orderStatus.value !== undefined ? scope.row.orderStatus.value : scope.row.orderStatus)" size="small">
            {{ getOrderStatusText(scope.row.orderStatus && scope.row.orderStatus.value !== undefined ? scope.row.orderStatus.value : scope.row.orderStatus) }}
          </el-tag>
          <div v-if="scope.row.refund_status == 0 && scope.row.refund && scope.row.refund.length" class="mt5">
            <el-tag type="warning" size="mini">退款中</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="160">
        <template slot-scope="scope">
          <a
            v-db-click
            @click="edit(scope.row)"
            v-if="scope.row._status === 1 && scope.row.is_del !== 1 && scope.row.is_cancel !== 1">编辑</a>
          <el-divider
            direction="vertical"
            v-if="scope.row._status === 1 && scope.row.is_del !== 1 && scope.row.is_cancel !== 1"
          />
          <el-divider
            direction="vertical"
            v-if="
              (scope.row.status === 4 || scope.row._status === 2 || scope.row._status === 8) &&
              scope.row.shipping_type === 1 &&
              (scope.row.pinkStatus === null || scope.row.pinkStatus === 2) &&
              scope.row.is_del !== 1 &&
              scope.row.is_cancel !== 1 &&
              !scope.row.refund.length
            "
          />
          <a v-db-click @click="delivery(scope.row)" v-if="scope.row._status === 4 && !scope.row.split.length">配送信息</a>
          <el-divider direction="vertical" v-if="scope.row._status === 4 && !scope.row.split.length" />
          <a
            v-db-click
            @click="bindWrite(scope.row)"
            v-if="
              scope.row.shipping_type == 2 &&
              scope.row.status == 0 &&
              (scope.row.payStatus === 2 || scope.row.pay_status === 2) &&
              scope.row.refund_status === 0
            "
            >立即核销</a
          >
          <el-divider
            direction="vertical"
            v-if="
              scope.row.shipping_type == 2 &&
              scope.row.status == 0 &&
              (scope.row.payStatus === 2 || scope.row.pay_status === 2) &&
              scope.row.refund_status === 0
            "
          />
          <template>
            <el-dropdown size="small" @command="changeMenu(scope.row, $event)" :transfer="true">
              <span class="el-dropdown-link"> 更多<i class="el-icon-arrow-down el-icon--right"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="1"
                  v-show="
                    scope.row._status === 1 &&
                    (scope.row.payStatus === 1 || scope.row.pay_status === 1) &&
                    scope.row.pay_type === 'offline' &&
                    scope.row.is_del !== 1 &&
                    scope.row.is_cancel !== 1
                  "
                  >确认付款</el-dropdown-item
                >
                <el-dropdown-item command="2">订单详情</el-dropdown-item>
                <el-dropdown-item 
                  command="14" 
                  v-show="canShip(scope.row)"
                  icon="el-icon-truck"
                >
                  订单发货
                </el-dropdown-item>
                <el-dropdown-item command="11" v-show="scope.row._status >= 3 && scope.row.express_dump"
                  >电子面单打印</el-dropdown-item
                >
                <el-dropdown-item command="10" v-show="scope.row._status >= 2">小票打印</el-dropdown-item>
                <el-dropdown-item
                  command="4"
                  v-show="
                    scope.row._status !== 1 ||
                    (scope.row._status === 3 &&
                      scope.row.use_integral > 0 &&
                      scope.row.use_integral >= scope.row.back_integral)
                  "
                  >订单备注</el-dropdown-item
                >
                <el-dropdown-item
                  command="5"
                  v-show="(scope.row.payStatus === 2 || scope.row.pay_status === 2) && scope.row.refund_status == 0 && !scope.row.refund.length"
                  >立即退款</el-dropdown-item
                >
                <!--                            <el-dropdown-item command="6"  v-show='scope.row._status !==1 && (scope.row.use_integral > 0 && scope.row.use_integral >= scope.row.back_integral) '>退积分</el-dropdown-item>-->
                <!--                            <el-dropdown-item command="7"  v-show='scope.row._status === 3'>不退款</el-dropdown-item>-->
                <el-dropdown-item command="8" v-show="scope.row._status === 4">已收货</el-dropdown-item>
                <el-dropdown-item command="9">删除订单</el-dropdown-item>
                <el-dropdown-item command="12" v-show="scope.row.kuaidi_label">快递面单打印</el-dropdown-item>
                <el-dropdown-item command="13" v-show="scope.row.payStatus === 2">配货单打印</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <div class="acea-row row-right page">
      <pagination v-if="total" :total="total" :page.sync="page.page" :limit.sync="page.limit" @pagination="getList" />
    </div>
    <!-- 编辑 退款 退积分 不退款-->
    <edit-from ref="edits" :FromData="FromData" @submitFail="submitFail"></edit-from>
    <!-- 详情 -->
    <details-from ref="details" :orderDatalist="orderDatalist" :orderId="orderId"></details-from>
    <!-- 备注 -->
    <order-remark ref="remarks" :orderId="orderId" @submitFail="submitFail"></order-remark>
    <!-- 取消寄件功能暂时移除 -->
    <!-- 快速发货 -->
    <quick-shipment ref="quickShipment" :orderId="orderId" @submitFail="submitFail"></quick-shipment>
    <!-- 发送货 -->
    <order-send
      ref="send"
      :orderId="orderId"
      :status="status"
      :pay_type="pay_type"
      :virtual_type="virtual_type"
      @submitFail="submitFail"
      @clearId="
        () => {
          orderId = 0;
          virtual_type = null;
        }
      "
    ></order-send>
    <order-refund
      ref="refund"
      :orderId="orderId"
      :status="status"
      :pay_type="pay_type"
      :virtual_type="virtual_type"
      @submitFail="submitFail"
      @clearId="
        () => {
          orderId = 0;
          virtual_type = null;
        }
      "
    ></order-refund>
    <!--    -->
    <el-dialog
      :visible.sync="modals2"
      title="订单核销"
      class="paymentFooter"
      :show-close="true"
      width="540px"
      @closed="changeModal"
    >
      <el-form
        ref="writeOffFrom"
        :model="writeOffFrom"
        :rules="writeOffRules"
        label-width="80px"
        label-position="right"
        class="tabform"
        @submit.native.prevent
      >
        <el-form-item prop="code" label="核销码：">
          <el-input
            style="width: 414px"
            type="text"
            placeholder="请输入12位核销码"
            v-model.number="writeOffFrom.code"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" v-db-click @click="ok('writeOffFrom')">立即核销</el-button>
        <el-button v-db-click @click="del('writeOffFrom')">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="batchShipmentModal"
      title="批量发货"
      class="paymentFooter"
      :show-close="true"
      width="540px"
      @closed="changeModal"
    >
      <!-- <el-upload :action="expressUrl" :headers="header" :on-success="upExpress">
        <el-button class="export" type="primary">批量发货</el-button>
      </el-upload> -->
      <el-alert type="warning" :closable="false">
        <p>步骤一 导出发货单</p>
        <p>步骤二 发货单中填写物流单号</p>
        <p>步骤三 将发货单上传</p>
      </el-alert>
      <div class="acea-row row-middle mb10 mt10">
        <el-button v-db-click @click="exportDeliveryList">导出发货单</el-button>
        <div class="pl20 tips"></div>
      </div>
      <el-upload
        class="upload-demo"
        accept=".doc,.docx,.xls,.xlsx"
        drag
        :action="expressUrl"
        :headers="header"
        :on-success="upExpress"
        :before-upload="beforeUpload"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">批量发货单,拖入上传或<em>点击上传</em></div>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import expandRow from './tableExpand.vue';
import printJS from 'print-js';
import {
  orderList,
  getOrdeDatas,
  getDataInfo,
  getRefundFrom,
  getnoRefund,
  refundIntegral,
  getDistribution,
  writeUpdate,
  shipmentCancelOrder,
  putWrite,
  importExpress,
  adminDeliver,
} from '@/api/order';
import { mapState, mapMutations } from 'vuex';
import editFrom from '../../../../components/from/from';
import detailsFrom from '../handle/orderDetails';
import orderRemark from '../handle/orderRemark';
import orderSend from '../handle/orderSend';
import orderRefund from '../handle/orderRefund';

import quickShipment from '../handle/quickShipment';
import { exportOrderList, exportOrderDeliveryList } from '@api/export';
import Setting from '@/setting';
import { getCookies } from '@/libs/util';
import createWorkBook from '@/vendor/newToExcel.js';
import { isFileUpload,formatImageUrl } from '@/utils';

export default {
  name: 'table_list',
  components: {
    expandRow,
    editFrom,
    detailsFrom,
    orderRemark,
    orderSend,

    orderRefund,
    quickShipment,
  },
  data() {
    const codeNum = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写核销码'));
      }
      // 模拟异步验证效果
      if (!Number.isInteger(value)) {
        callback(new Error('请填写12位数字'));
      } else {
        const reg = /\b\d{12}\b/;
        if (!reg.test(value)) {
          callback(new Error('请填写12位数字'));
        } else {
          callback();
        }
      }
    };
    return {
      batchShipmentModal: false,
      expressUrl: Setting.apiBaseURL + '/file/upload/1',
      header: {},
      delfromData: {},
      modal: false,
      orderList: [],
      orderCards: [],
      loading: false,
      orderId: 0,
      total_num: 0,
      virtual_type: 0,
      status: 0,
      pay_type: '',

      total: 0, // 总条数
      page: {
        page: 1, // 当前页
        limit: 15, // 每页显示条数
      },
      data: [],
      FromData: null,
      orderDatalist: null,
      // modalTitleSs: '',
      selectedIds: [], //选中合并项的id
      currentTab: 'null',
      spinShow: false,
      tablists: {
        all: '0',
        general: '0',
        pink: '0',
        seckill: '0',
        bargain: '0',
        advance: '0',
      },
      writeOffRules: {
        code: [{ validator: codeNum, trigger: 'blur', required: true }],
      },
      writeOffFrom: {
        code: '',
        confirm: 0,
      },
      modals2: false,
    };
  },
  computed: {
    ...mapState('order', [
      'orderPayType',
      'orderStatus',
      'orderTime',
      'real_name',
      'fieldKey',
      'orderType',
      'delIdList',
      'isDels',
      'orderChartType',
    ]),
    // 检查是否有可发货的选中订单
    hasShippableOrders() {
      if (!this.selectedIds || this.selectedIds.length === 0) {
        return false;
      }
      return this.orderList.some(order => 
        this.selectedIds.includes(order.id) && this.canShip(order)
      );
    }
  },
  mounted() {},
  created() {
    this.getTabs();
    this.onChangeTabs('');
    this.getList();
    this.getToken();
  },
  watch: {
    orderType: function () {
      this.page.page = 1;
      this.getList();
    },
  },
  methods: {
    ...mapMutations('order', ['getOrderStatus', 'onChangeTabs', 'getIsDel', 'getisDelIdListl']),
    batchShipment() {},
    beforeUpload(file) {
      return isFileUpload(file);
    },
    fir(url){
      return formatImageUrl(url)
    },
    // 判断是否可以发货
    canShip(row) {
      const orderStatus = row.orderStatus && row.orderStatus.value !== undefined ? row.orderStatus.value : row.orderStatus;
      const payStatus = row.payStatus && row.payStatus.value !== undefined ? row.payStatus.value : row.payStatus;
      
      return orderStatus === 1 &&  // 待发货状态 (WAIT_SEND)
             payStatus === 2 &&     // 已支付状态 (PAID)
             row.is_del !== 1;      // 未删除
    },
    // 快速批量发货
    quickBatchShip() {
      if (!this.selectedIds || this.selectedIds.length === 0) {
        this.$message.warning('请先选择要发货的订单');
        return;
      }
      
      // 筛选出可发货的订单
      const shippableOrders = this.orderList.filter(order => 
        this.selectedIds.includes(order.id) && this.canShip(order)
      );
      
      if (shippableOrders.length === 0) {
        this.$message.warning('选中的订单中没有可发货的订单');
        return;
      }
      
      const orderCount = shippableOrders.length;
      const orderIds = shippableOrders.map(order => order.id);
      
      this.$confirm(
        `确定要对选中的 ${orderCount} 个订单进行快速发货吗？\n（将自动设置为已发货状态，无需填写快递信息）`,
        '批量发货确认',
        {
          confirmButtonText: '确定发货',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--success'
        }
      ).then(() => {
        this.processBatchShipment(orderIds);
      }).catch(() => {
        // 用户取消操作
      });
    },
    // 处理批量发货
    async processBatchShipment(orderIds) {
      this.loading = true;
      let successCount = 0;
      let failCount = 0;
      
      try {
        // 逐个处理发货
        for (const orderId of orderIds) {
          try {
            const deliveryData = {
              orderId: orderId,
              expressType: '快速发货',
              expressCode: 'QUICK',
              trackingNumber: 'BATCH_' + Date.now()
            };
            
            await adminDeliver(deliveryData);
            successCount++;
          } catch (error) {
            console.error(`订单 ${orderId} 发货失败:`, error);
            failCount++;
          }
        }
        
        // 显示结果
        if (successCount > 0) {
          this.$message.success(`成功发货 ${successCount} 个订单${failCount > 0 ? `，失败 ${failCount} 个` : ''}`);
          this.getList(); // 刷新列表
          this.$emit('changeGetTabs'); // 更新统计
        } else {
          this.$message.error('批量发货失败，请检查订单状态');
        }
        
      } catch (error) {
        this.$message.error('批量发货操作失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },
    // 获取订单状态文本
    getOrderStatusText(status) {
      // 处理枚举对象或直接值
      const statusValue = status && status.value !== undefined ? status.value : status;
      const statusMap = {
        0: '待支付',   // WAIT_PAY
        1: '待发货',   // WAIT_SEND
        2: '待收货',   // WAIT_RECEIVE
        3: '已完成',   // COMPLETED
        4: '已取消',   // CANCELLED
        5: '待核销'
      };
      return statusMap[statusValue] || '未知状态';
    },
    // 获取订单状态标签类型
    getOrderStatusType(status) {
      // 处理枚举对象或直接值
      const statusValue = status && status.value !== undefined ? status.value : status;
      const typeMap = {
        0: 'warning',    // 待支付
        1: 'primary',    // 待发货
        2: 'info',       // 待收货
        3: 'success',    // 已完成
        4: 'danger',     // 已取消
        5: 'primary'     // 待核销
      };
      return typeMap[statusValue] || '';
    },
    // 获取支付状态文本
    getPayStatusText(payStatus) {
      // 处理枚举对象或直接值
      const statusValue = payStatus && payStatus.value !== undefined ? payStatus.value : payStatus;
      const statusMap = {
        0: '到付',       // CASH_ON_DELIVERY
        1: '待付款',     // PENDING_PAYMENT
        2: '已支付',     // PAID
        3: '待退款',     // PENDING_REFUND
        4: '退款成功',   // REFUND_SUCCESSFULLY
        5: '退款失败'    // REFUND_FAILED
      };
      return statusMap[statusValue] || '未支付';
    },
    // 获取支付状态标签类型
    getPayStatusType(payStatus) {
      // 处理枚举对象或直接值
      const statusValue = payStatus && payStatus.value !== undefined ? payStatus.value : payStatus;
      const typeMap = {
        0: 'info',       // 到付
        1: 'warning',    // 待付款
        2: 'success',    // 已支付
        3: 'warning',    // 待退款
        4: 'success',    // 退款成功
        5: 'danger'      // 退款失败
      };
      return typeMap[statusValue] || 'warning';
    },
    // 操作
    changeMenu(row, name) {
      this.orderId = row.id;
      switch (name) {
        case '1':
          this.delfromData = {
            title: '修改订单为已支付',
            url: `/order/pay_offline/${row.id}`,
            method: 'post',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.$emit('changeGetTabs');
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          // this.modalTitleSs = '修改立即支付';
          break;
        case '2':
          this.getData(row.id);
          break;
        case '4':
          this.$refs.remarks.modals = true;
          this.$refs.remarks.formValidate.remark = row.remark;
          break;
        case '5':
          // this.getRefundData(row.id);
          this.$refs.refund.total_num = row.total_num;
          this.$refs.refund.order_id = row.order_id;
          this.$refs.refund.formItem.refund_price = row.pay_price;
          this.virtual_type = row.virtual_type;
          this.$refs.refund.modals = true;
          this.orderId = row.id;
          this.status = row._status;
          this.pay_type = row.pay_type;
          break;
        // case '6':
        //   this.getRefundIntegral(row.id);
        //   break;
        // case '7':
        //   this.getNoRefundData(row.id);
        //   break;
        case '8':
          this.delfromData = {
            title: '修改确认收货',
            url: `/order/take/${row.id}`,
            method: 'put',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          // this.modalTitleSs = '修改确认收货';
          break;
        case '10':
          this.delfromData = {
            title: '立即打印订单',
            url: `/order/print/${row.id}`,
            method: 'get',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.$emit('changeGetTabs');
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          break;
        case '11':
          this.delfromData = {
            title: '立即打印电子面单',
            info: '您确认打印此电子面单吗?',
            url: `/order/order_dump/${row.id}`,
            method: 'get',
            ids: '',
          };
          this.$modalSure(this.delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
          break;
        case '12':
          this.printImg(row.kuaidi_label);
          break;
        case '13':
          let pathInfo = this.$router.resolve({
            path: Setting.routePre + '/order/print',
            query: {
              id: row.order_id,
            },
          });
          window.open(pathInfo.href, '_blank');
          break;
        case '14':
          // 订单发货
          this.quickShipment(row);
          break;
        default:
          this.delfromData = {
            title: '删除订单',
            url: `/order/del/${row.id}`,
            method: 'DELETE',
            ids: '',
          };
          // this.modalTitleSs = '删除订单';
          this.delOrder(row, this.delfromData);
      }
    },
    // shipmentClear功能暂时移除
    // shipmentClear(row) {
    //   this.orderId = row.id;
    //   this.$refs.shipment.modals = true;
    // },
    printImg(url) {
      printJS({
        printable: url,
        type: 'image',
        documentTitle: '快递信息',
        style: `img{
          width: 100%;
          height: 476px;
        }`,
      });
    },
    // 立即支付 /确认收货//删除单条订单
    submitModel() {
      this.getList();
    },
    // 订单列表
    getList(pageNum) {
      // 处理分页参数
      if (pageNum === 1) {
        // 重置到第一页（用于搜索等操作）
        this.page.page = 1;
      }
      // 由于使用了.sync修饰符，页码和每页条数会自动同步到this.page
      // 所以这里直接使用this.page.page和this.page.limit即可
      
      console.log('getList called with pageNum:', pageNum, 'current page:', this.page.page, 'limit:', this.page.limit);
      console.log('订单状态参数:', this.orderStatus, '类型:', typeof this.orderStatus);
      this.loading = true;
      
      const params = {
        pageNum: this.page.page,    // 后端期望的参数名
        pageSize: this.page.limit,  // 后端期望的参数名
        orderStatus: this.orderStatus === '' || this.orderStatus === 'null' ? null : parseInt(this.orderStatus),  // 修正参数名和格式
        keyword: this.real_name || null,  // 搜索关键词
      };

      // 过滤掉 null 和 undefined 值
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      console.log('最终发送的参数:', params);

      orderList(params)
        .then((res) => {
          console.log('API返回数据:', res.data);
          const data = res.data;
          
          // 根据后端返回的Page结构解析数据
          if (data && data.records) {
            this.orderList = data.records || [];
            this.total = data.total || 0;
          } else {
            // 如果数据结构不同，尝试其他可能的结构
            this.orderList = data.list || data || [];
            this.total = data.total || this.orderList.length;
          }
          
          console.log('解析后的订单列表:', this.orderList.length, '条记录');
          
          this.$nextTick(() => {
            this.setChecked();
          });
          
          this.$emit('on-changeCards', data.stat || {});
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          this.$message.error(error.msg || '获取订单列表失败');
        });
    },
    handleSelectRow(selection) {
      let ids = [];
      selection.map((e) => {
        ids.push(e.id);
      });
      this.selectedIds = ids;
      this.$nextTick(() => {
        //确保dom加载完毕
        this.setChecked();
      });
    },
    setChecked() {
      //将new Set()转化为数组
      let ids = [...this.selectedIds];
      this.getisDelIdListl(ids);
      // 找到绑定的table的ref对应的dom，找到table的objData对象，objData保存的是当前页的数据
      let objData = this.$refs.table.objData;
      for (let index in objData) {
        if (this.selectedIds.has(objData[index].id)) {
          objData[index]._isChecked = true;
        }
      }
    },
    isDel(selection) {
      if (selection.findIndex((target) => target.is_del === 0) == -1) {
        this.getIsDel(1);
      } else {
        this.getIsDel(0);
      }
    },
    // 编辑
    edit(row) {
      this.getOrderData(row.id);
    },
    // 删除单条订单
    delOrder(row, data) {
      if (row.is_del === 1) {
        this.$modalSure(data)
          .then((res) => {
            this.$message.success(res.msg);
            this.getList();
          })
          .catch((res) => {
            this.$message.error(res.msg);
          });
      } else {
        this.$message.error('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！');
      }
    },
    // 获取编辑表单数据
    getOrderData(id) {
      getOrdeDatas(id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 获取详情表单数据
    getData(id) {
      getDataInfo(id)
        .then(async (res) => {
          this.orderId = id;
          this.orderDatalist = res.data;
          console.log("orderDatalist", this.orderDatalist)
          this.$refs.details.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 修改成功
    submitFail() {
      this.getList();
      this.$emit('changeGetTabs');
    },
    // 获取退款表单数据
    getRefundData(id) {
      this.$modalForm(getRefundFrom(id)).then(() => {
        this.getList();
        this.$emit('changeGetTabs');
      });
    },
    // 获取退积分表单数据
    getRefundIntegral(id) {
      refundIntegral(id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 不退款表单数据
    getNoRefundData(id) {
      this.$modalForm(getnoRefund(id)).then(() => {
        this.getList();
        this.$emit('changeGetTabs');
      });
    },
    // 发送货
    sendOrder(row) {
      if (row.user_address) {
        this.$refs.send.userSendmsg = {
          real_name: row.real_name,
          user_address: row.user_address,
          user_phone: row.user_phone,
        };
      }
      this.$refs.send.total_num = row.total_num;
      this.virtual_type = row.virtual_type;
      this.$refs.send.modals = true;
      this.orderId = row.id;
      this.status = row._status;
      this.pay_type = row.pay_type;
      this.$refs.send.getList();
      this.$refs.send.getDeliveryList();
      this.$nextTick((e) => {
        this.$refs.send.getCartInfo(row._status, row.id);
      });
    },
    // 配送信息表单数据
    delivery(row) {
      getDistribution(row.id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 核销订单
    bindWrite(row) {
      let self = this;
      this.$msgbox({
        title: '提示',
        message: '确定要核销该订单吗？',
        showCancelButton: true,
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        iconClass: 'el-icon-warning',
        confirmButtonClass: 'btn-custom-cancel',
      })
        .then(() => {
          writeUpdate(row.order_id)
            .then((res) => {
              self.$message.success(res.msg);
              self.getList();
            })
            .catch((res) => {
              self.$message.error(res.msg);
            });
        })
        .catch(() => {});
    },
    // 获取订单统计数据
    getTabs() {
      // 简化统计数据获取，只在需要时获取
      this.tablists = { all: '0' };
    },
    onClickTab() {
      console.log('点击标签页:', this.currentTab);
      const statusValue = this.currentTab === 'null' ? '' : this.currentTab;
      console.log('设置订单状态为:', statusValue);
      this.getOrderStatus(statusValue);
      this.page.page = 1; // 重置到第一页
      this.getList();
    },
    // 批量删除
    delAll() {
      if (this.delIdList.length === 0) {
        this.$message.error('请先选择删除的订单！');
      } else {
        if (this.isDels) {
          let idss = {
            ids: this.delIdList,
          };
          let delfromData = {
            title: '删除订单',
            url: `/order/dels`,
            method: 'post',
            ids: idss,
          };
          this.$modalSure(delfromData)
            .then((res) => {
              this.$message.success(res.msg);
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg);
            });
        } else {
          this.$message.error('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！');
        }
      }
    },
    // 下载批量发货模版
    async exportDeliveryList() {
      let [th, filekey, data, fileName] = [[], [], [], ''];
      let deliveryData = { page: 1, limit: 200 };
      for (let i = 0; i < deliveryData.page + 1; i++) {
        let expressData = await this.getDeliveryData(deliveryData);
        if (!fileName) fileName = expressData.filename;
        if (!filekey.length) {
          filekey = expressData.fileKey;
        }
        if (!th.length) th = expressData.header;
        if (expressData.export.length) {
          data = data.concat(expressData.export);
          deliveryData.page++;
        } else {
          this.$exportExcel(th, filekey, fileName, data);
          return;
        }
      }
    },
    getDeliveryData(deliveryData) {
      return new Promise((resolve, reject) => {
        exportOrderDeliveryList(deliveryData).then((res) => {
          resolve(res.data);
        });
      });
    },
    // 上传头部token
    getToken() {
      this.header['Authori-zation'] = 'Bearer ' + getCookies('token');
    },
    upExpress(data) {
      importExpress({ file: data.data.src })
        .then((res) => {
          this.$message.success(res.msg);
          this.getList();
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 导出
    async exportList() {
      let excelData = {
          page: 1,
          limit: 100,
          status: this.orderStatus,
          pay_type: this.orderPayType,
          data: this.orderTime,
          real_name: this.real_name,
          field_key: this.fieldKey,
          type: this.orderType === 0 ? '' : this.orderType,
          ids: this.delIdList,
        },
        data = [],
        lebData = {};
      for (let i = 1; i < excelData.page + 1; i++) {
        lebData = await this.getExcelData(excelData);
        if (lebData.export.length) {
          data = data.concat(lebData.export);
          if (lebData.export.length == excelData.limit) excelData.page++;
        }
      }
      createWorkBook(lebData.header, lebData.filename, data, '', lebData.filename);
    },
    getExcelData(excelData) {
      return new Promise((resolve, reject) => {
        exportOrderList(excelData).then((res) => {
          resolve(res.data);
        });
      });
    },
    // 订单核销
    writeOff() {
      this.modals2 = true;
    },
    // 订单核销
    ok(name) {
      if (!this.writeOffFrom.code) {
        this.$message.warning('请先验证订单！');
      } else {
        this.writeOffFrom.confirm = 1;
        putWrite(this.writeOffFrom)
          .then(async (res) => {
            if (res.success) {
              this.$message.success(res.msg);
              this.modals2 = false;
              this.$refs[name].resetFields();
              this.getList();
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((res) => {
            this.$message.error(res.msg);
          });
      }
    },
    del(name) {
      this.modals2 = false;
      this.writeOffFrom.code = '';
      this.$refs[name].resetFields();
    },
    changeModal() {
      this.writeOffFrom.code = '';
    },
    // 快速发货
    quickShipment(row) {
      this.orderId = row.id;
      this.$refs.quickShipment.modals = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload,
::v-deep .el-upload-dragger {
  width: 100%;
}

::v-deep .el-upload-list {
  display: none;
}

::v-deep .el-tabs__item {
  height: 54px;
  line-height: 54px;
}

img {
  height: 36px;
  display: block;
}

.tabBox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 2px;

  .tabBox_img {
    width: 36px;
    height: 36px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .tabBox_tit {
    width: 60%;
    font-size: 12px !important;
    margin: 0 10px 0 10px;
    letter-spacing: 1px;
    padding: 5px 0;
    box-sizing: border-box;
  }
}

.orderData ::v-deep .ivu-table-cell {
  padding-left: 0 !important;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.nickname {
}

.uid {
  color: #2d8cf0;
}

.pink_name {
  color: #666;
}

.tab {
  display: flex;
  align-items: center;

  img {
    width: 36px;
    height: 36px;
    margin-right: 10px;
  }
}

.w-250 {
  max-width: 250px;
}

.w-120 {
  width: 120px;
}

.tips {
  color: #c0c4cc;
  font-size: 12px;
}
</style>
// 订单状态样式
.order-sn {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.order-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.order-status-tag {
  margin-top: 4px;
}

.text-primary {
  color: #409EFF;
  font-weight: 500;
}

.text-muted {
  color: #909399;
}

.text-sm {
  font-size: 12px;
}

.mt5 {
  margin-top: 5px;
}

<style scoped>
.ship-actions {
  display: inline-block;
}

.ship-actions .el-button {
  margin-right: 5px;
}

.ship-actions .el-button--mini {
  padding: 5px 8px;
  font-size: 12px;
}

/* 发货按钮特殊样式 */
.ship-actions .el-button--primary {
  background-color: #67C23A;
  border-color: #67C23A;
}

.ship-actions .el-button--primary:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}
</style>