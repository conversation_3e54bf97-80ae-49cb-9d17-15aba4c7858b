<template>
  <el-dialog
    :visible.sync="modals"
    title="订单发货"
    class="order_box"
    :show-close="true"
    width="600px"
    @closed="changeModal"
  >
    <el-form
      v-if="modals"
      ref="formItem"
      :rules="ruleValidate"
      :model="formItem"
      label-width="100px"
      @submit.native.prevent
      v-loading="isLoading"
    >
      <el-form-item label="快递公司：" prop="delivery_name">
        <el-select
          v-model="formItem.delivery_name"
          filterable
          placeholder="请选择快递公司"
          style="width: 100%"
          @change="expressChange"
        >
          <el-option
            v-for="item in express"
            :value="item.value"
            :key="item.value"
            >{{ item.value }}</el-option
          >
        </el-select>
      </el-form-item>
      
      <el-form-item label="快递单号：" prop="delivery_id">
        <el-input 
          v-model="formItem.delivery_id" 
          placeholder="请输入快递单号" 
          style="width: 100%"
        ></el-input>
<!--        <div class="trips" v-if="formItem.delivery_name == '顺丰速运'">
          <p>顺丰请输入单号:收件人或寄件人手机号后四位，</p>
          <p>例如：SF000000000000:3941</p>
        </div>-->
      </el-form-item>
    </el-form>
    
    <div slot="footer">
      <el-button v-db-click @click="cancel">取消</el-button>
      <el-button type="primary" v-db-click @click="submitShipment">确认发货</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getExpressData, putDelivery, adminDeliver } from '@/api/order';

export default {
  name: 'quickShipment',
  props: {
    orderId: Number,
  },
  data() {
    return {
      modals: false,
      isLoading: false,
      express: [],
      formItem: {
        type: '1', // 发货类型
        express_record_type: '1', // 录入单号
        delivery_name: '',
        delivery_code: '',
        delivery_id: '',
      },
      ruleValidate: {
        delivery_name: [
          { required: true, message: '请选择快递公司', trigger: 'change' }
        ],
        delivery_id: [
          { required: true, message: '请输入快递单号', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    this.getExpressList();
  },
  methods: {
    // 获取快递公司列表
    getExpressList() {
      this.isLoading = true;
      getExpressData('')
        .then((res) => {
          this.express = res.data;
          console.log("express===>",this.express)
          this.isLoading = false;
        })
        .catch((res) => {
          this.isLoading = false;
          this.$message.error(res.msg);
        });
    },
    
    // 快递公司选择变化
    expressChange(value) {
      let expressItem = this.express.find((item) => {
        return item.value === value;
      });
      if (expressItem) {
        this.formItem.delivery_code = expressItem.code;
      }
    },
    
    // 提交发货
    submitShipment() {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          const deliveryData = {
            orderId: this.orderId,
            expressType: this.formItem.delivery_name,
            expressCode: this.formItem.delivery_code,
            trackingNumber: this.formItem.delivery_id
          };
          
          // 调用管理员发货接口
          adminDeliver(deliveryData)
            .then((res) => {
              this.modals = false;
              this.$message.success(res.msg || '发货成功');
              this.$emit('submitFail');
              this.reset();
            })
            .catch((res) => {
              this.$message.error(res.msg || '发货失败');
            });
        }
      });
    },
    
    // 取消
    cancel() {
      this.modals = false;
      this.reset();
    },
    
    // 重置表单
    reset() {
      this.formItem = {
        type: '1',
        express_record_type: '1',
        delivery_name: '',
        delivery_code: '',
        delivery_id: '',
      };
      if (this.$refs.formItem) {
        this.$refs.formItem.resetFields();
      }
    },
    
    // 关闭弹窗
    changeModal() {
      this.reset();
    }
  },
};
</script>

<style scoped>
.trips {
  color: #ccc;
  font-size: 12px;
  margin-top: 5px;
}
</style>