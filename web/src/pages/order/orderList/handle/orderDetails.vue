<template>
  <div>
    <el-drawer title="订单详情" :size="1000" :visible.sync="modals" wrapperClosable :before-close="handleClose">
      <div v-if="orderDatalist">
        <div class="head">
          <div class="full">
            <img class="order_icon" :src="orderImg" alt="" />
            <div class="text">
              <div class="title">普通订单</div>
              <div>
                <span class="mr20">订单号：{{ orderDatalist.orderInfo.orderSN }}</span>
              </div>
            </div>
          </div>
          <ul class="list">
            <li class="item">
              <div class="title">订单状态</div>
              <div>
                {{ getOrderStatusText(orderDatalist.orderInfo.orderStatus) }}
              </div>
            </li>
            <li class="item">
              <div class="title">实际支付</div>
              <div>¥ {{ orderDatalist.orderInfo.actualPrice || '0.0' }}</div>
            </li>
            <li class="item">
              <div class="title">支付方式</div>
              <div>{{ orderDatalist.orderInfo.payName || '未支付' }}</div>
            </li>
            <li class="item">
              <div class="title">支付时间</div>
              <div>{{ formatDate(orderDatalist.orderInfo.payTime) || '未支付' }}</div>
            </li>
          </ul>
        </div>
        <el-tabs type="border-card" v-model="activeName" @tab-click="tabClick">
          <el-tab-pane label="订单信息" name="detail">
            <div class="section">
              <div class="title">用户信息</div>
              <ul class="list">
                <li class="item">
                  <div>用户ID：</div>
                  <div class="value">{{ orderDatalist.orderInfo.userId }}</div>
                </li>
                <li class="item">
                  <div>联系电话：</div>
                  <div class="value">{{ orderDatalist.orderInfo.mobile || '' }}</div>
                </li>
              </ul>
            </div>
            <div class="section">
              <div class="title">收货信息</div>
              <ul class="list">
                <li class="item">
                  <div>收货人：</div>
                  <div class="value">
                    {{ orderDatalist.orderInfo.consignee ? orderDatalist.orderInfo.consignee : '-' }}
                  </div>
                </li>
                <li class="item">
                  <div>收货电话：</div>
                  <div class="value">
                    {{ orderDatalist.orderInfo.mobile ? orderDatalist.orderInfo.mobile : '-' }}
                  </div>
                </li>
                <li class="item">
                  <div>收货地址：</div>
                  <div class="value">
                    {{ orderDatalist.orderInfo.fullRegion }} {{ orderDatalist.orderInfo.address || '-' }}
                  </div>
                </li>
              </ul>
            </div>
            <div class="section">
              <div class="title">订单信息</div>
              <ul class="list">
                <li class="item">
                  <div>创建时间：</div>
                  <div class="value">{{ formatDate(orderDatalist.orderInfo.createTime) }}</div>
                </li>
                <li class="item">
                  <div>商品总数：</div>
                  <div class="value">{{ getTotalQuantity(orderDatalist.orderGoods) }}</div>
                </li>
                <li class="item">
                  <div>商品总价：</div>
                  <div class="value">{{ orderDatalist.orderInfo.goodsPrice }}</div>
                </li>
                <li class="item">
                  <div>优惠券金额：</div>
                  <div class="value">{{ orderDatalist.orderInfo.couponPrice }}</div>
                </li>
                <li class="item">
                  <div>积分抵扣：</div>
                  <div class="value">{{ orderDatalist.orderInfo.integralMoney || '0.0' }}</div>
                </li>
                <li class="item">
                  <div>交付邮费：</div>
                  <div class="value">{{ orderDatalist.orderInfo.freightPrice }}</div>
                </li>
                <li class="item">
                  <div>实际支付：</div>
                  <div class="value">{{ orderDatalist.orderInfo.actualPrice || '0.0' }}</div>
                </li>
              </ul>
            </div>
            <div class="section" v-if="orderDatalist.orderInfo.orderExpress">
              <div class="title">物流信息</div>
              <ul class="list">
                <li class="item">
                  <div>物流公司：</div>
                  <div class="value">
                    {{ orderDatalist.orderInfo.orderExpress ? orderDatalist.orderInfo.orderExpress.shipperName : '-' }}
                  </div>
                </li>
                <li class="item">
                  <div>物流单号：</div>
                  <div class="value">
                    {{ orderDatalist.orderInfo.orderExpress ? orderDatalist.orderInfo.orderExpress.logisticCode : '-' }}
                    <a v-if="orderDatalist.orderInfo.orderExpress" v-db-click @click="openLogistics">物流查询</a>
                  </div>
                </li>
              </ul>
            </div>
            <div class="section">
              <div class="title">买家留言</div>
              <ul class="list">
                <li class="item">
                  <div>{{ orderDatalist.orderInfo.postscript ? orderDatalist.orderInfo.postscript : '-' }}</div>
                </li>
              </ul>
            </div>
          </el-tab-pane>
          <el-tab-pane label="商品信息" name="goods">
            <el-table class="mt20" :data="orderDatalist.orderGoods">
              <el-table-column label="商品信息" min-width="300">
                <template slot-scope="scope">
                  <div class="tab">
                    <div class="demo-image__preview">
                      <el-image
                        :src="formatImageUrl(scope.row.listPicUrl)"
                        :preview-src-list="[scope.row.listPicUrl]"
                      />
                    </div>
                    <div>
                      <div class="line">{{ scope.row.goodsName }}</div>
                      <div class="line1 gary">
                        规格：{{ scope.row.goodsSpecificationNameValue || '默认' }}
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="支付价格" min-width="90">
                <template slot-scope="scope">
                  <div class="tab">
                    <div class="line1">
                      {{ scope.row.retailPrice }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="购买数量" min-width="90">
                <template slot-scope="scope">
                  <div class="tab">
                    <div class="line1">
                      {{ scope.row.number }}
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="物流信息" name="logistics">
            <div class="logistics-info" v-if="orderDatalist.orderInfo.orderExpress">
              <div class="section">
                <div class="title">物流基本信息</div>
                <ul class="list">
                  <li class="item">
                    <div>物流公司：</div>
                    <div class="value">
                      {{ orderDatalist.orderInfo.orderExpress ? orderDatalist.orderInfo.orderExpress.shipperName : '-' }}
                    </div>
                  </li>
                  <li class="item">
                    <div>物流单号：</div>
                    <div class="value">
                      {{ orderDatalist.orderInfo.orderExpress ? orderDatalist.orderInfo.orderExpress.logisticCode : '-' }}
                    </div>
                  </li>
                  <li class="item">
                    <div>发货时间：</div>
                    <div class="value">
                      {{ formatDate(orderDatalist.orderInfo.orderExpress.createTime) || '-' }}
                    </div>
                  </li>
                </ul>
              </div>
              <div class="section">
                <div class="title">物流轨迹</div>
                <div class="logistics-timeline">
                  <el-timeline>
                    <el-timeline-item
                      v-for="(item, i) in orderExpress"
                      :key="i"
                      :timestamp="item.datetime || item.time"
                      placement="top"
                      :type="getTimelineItemType(item.status)"
                    >
                      <p>{{ item.content || item.status }}</p>
                    </el-timeline-item>
                    <el-timeline-item v-if="!orderExpress.length" timestamp="" placement="top">
                      <p>暂无物流信息</p>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
            </div>
            <div class="empty-logistics" v-else>
              <el-empty description="暂无物流信息"></el-empty>
                  </div>
          </el-tab-pane>
          <el-tab-pane label="订单记录" name="orderList" v-if="showOrderRecord">
            <el-table class="mt20" :data="recordData" v-loading="loading" empty-text="暂无数据" highlight-current-row>
              <el-table-column label="订单ID" min-width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.oid }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作记录" min-width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.change_message }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作时间" min-width="100">
                <template slot-scope="scope">
                  <span>{{ scope.row.change_time }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
    <el-drawer :visible.sync="modal2" scrollable title="物流查询" width="350px" class="order_box2">
      <div class="logistics acea-row row-top" v-if="orderDatalist && orderDatalist.orderInfo.orderExpress">
        <div class="logistics_img">
          <img src="../../../../assets/images/expressi.jpg" />
        </div>
        <div class="logistics_cent">
          <span>物流公司：{{ orderDatalist.orderInfo.orderExpress.shipperName }}</span>
          <span>物流单号：{{ orderDatalist.orderInfo.orderExpress.logisticCode }}</span>
        </div>
      </div>
      <div class="acea-row row-column-around trees-coadd">
        <div class="scollhide">
          <el-timeline>
            <el-timeline-item
              v-for="(item, i) in orderExpress"
              :key="i"
              :type="getTimelineItemType(item.status)"
            >
              <p class="time" v-text="item.datetime || item.time"></p>
              <p class="content" v-text="item.content || item.status"></p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { getOrderRecord } from '@/api/order';
import { formatImageUrl } from "@/utils";
export default {
  name: 'orderDetails',
  data() {
    return {
      activeName: 'detail',
      modal2: false,
      modals: false,
      grid: {
        xl: 8,
        lg: 8,
        md: 12,
        sm: 24,
        xs: 24,
      },
      showOrderRecord: false,
      orderExpress: [],
      orderImg: require('@/assets/images/order_icon.png'),
      recordData: [],
      page: {
        page: 1, // 当前页
        limit: 15, // 每页显示条数
      },
      loading: false,
      orderStatusMap: {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消',
        // 兼容旧的字符串键
        'WAIT_PAY': '待支付',
        'PAID': '已支付',
        'SHIPPED': '已发货',
        'CONFIRMED': '已确认',
        'FINISHED': '已完成',
        'CANCELED': '已取消'
      },
      payStatusMap: {
        0: '到付',
        1: '待付款',
        2: '已支付',
        3: '待退款',
        4: '退款成功',
        5: '退款失败',
        // 兼容旧的字符串键
        'PENDING_PAYMENT': '待支付',
        'PAID': '已支付',
        'REFUNDED': '已退款'
      }
    };
  },
  props: {
    orderDatalist: {
      type: Object,
      default: () => ({
        orderInfo: {},
        orderGoods: [],
        handleOption: {}
      }),
    },
    orderId: Number,
    is_refund: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    modals(val) {
      if (val) {
        this.activeName = 'detail';
        // Get logistics data directly from orderDatalist when modal is opened
        if (this.orderDatalist && this.orderDatalist.orderInfo && this.orderDatalist.orderInfo.orderExpress) {
          this.parseLogisticsData();
        }
      }
    },
    activeName(newVal) {
      if (newVal === 'logistics') {
        this.parseLogisticsData();
      } else if (newVal === 'orderList') {
        this.getRecordList();
      }
    },
    'orderDatalist.orderInfo.orderExpress': {
      handler(newVal) {
        if (newVal) {
          this.parseLogisticsData();
        }
      },
      deep: true
    }
  },
  mounted() {
    // Initialize data if needed
    if (this.orderDatalist && this.orderDatalist.orderInfo && this.orderDatalist.orderInfo.orderExpress) {
      this.parseLogisticsData();
    }
  },
  methods: {
    formatImageUrl,
    openLogistics() {
      this.modal2 = true;
    },
    // 解析物流数据
    parseLogisticsData() {
      console.log('Parsing logistics data from orderDatalist');
      if (this.orderDatalist && this.orderDatalist.orderInfo && this.orderDatalist.orderInfo.orderExpress) {
        const orderExpress = this.orderDatalist.orderInfo.orderExpress;
        if (orderExpress.traces) {
          try {
            this.orderExpress = JSON.parse(orderExpress.traces);
            console.log('Parsed logistics data:', this.orderExpress);
          } catch (e) {
            console.error('Error parsing logistics data:', e);
            this.orderExpress = [];
          }
        } else {
          this.orderExpress = [];
        }
      } else {
        this.orderExpress = [];
      }
    },
    tabClick(tab) {
      console.log('Tab clicked:', tab.name, 'Order ID:', this.orderId);
      if (tab.name == 'orderList') {
        this.getRecordList();
      } else if (tab.name == 'logistics') {
        this.parseLogisticsData();
      }
    },
    // 获取物流信息 - 不再使用
    getLogisticsData() {
      // This method is no longer used - data comes directly from orderDatalist
      console.log('getLogisticsData is deprecated, using parseLogisticsData instead');
      this.parseLogisticsData();
    },
    handleClose() {
      this.modals = false;
    },
    getRecordList() {
      let data = {
        id: this.is_refund ? this.orderDatalist.orderInfo.store_order_id : this.orderDatalist.orderInfo.id,
        datas: this.page,
      };
      this.loading = true;
      getOrderRecord(data)
        .then(async (res) => {
          this.recordData = res.data;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg);
        });
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    // 获取订单状态文本
    getOrderStatusText(status) {
      return this.orderStatusMap[status] || status;
    },
    // 计算商品总数量
    getTotalQuantity(goods) {
      if (!goods || !goods.length) return 0;
      return goods.reduce((sum, item) => sum + item.number, 0);
    },
    getTimelineItemType(status) {
      // Return different timeline item types based on the status
      if (!status) return 'primary';

      switch(status.toUpperCase()) {
        case 'SIGN':
          return 'success'; // 签收状态显示为绿色
        case 'DELIVERING':
          return 'warning'; // 派送中状态显示为黄色
        case 'ACCEPT':
          return 'info'; // 揽收状态显示为蓝色
        case 'TRANSPORT':
          return 'primary'; // 运输中状态显示为默认色
        default:
          return 'primary'; // 其他状态显示为默认色
      }
    }
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom: none;
}
::v-deep .el-tabs__item {
  height: 40px !important;
  line-height: 40px !important;
}
.head {
  padding: 0 30px 24px;

  .full {
    display: flex;
    align-items: center;
    .order_icon {
      width: 60px;
      height: 60px;
    }
    .iconfont {
      color: var(--prev-color-primary);
      &.sale-after {
        color: #90add5;
      }
    }
    .text {
      align-self: center;
      flex: 1;
      min-width: 0;
      padding-left: 12px;
      font-size: 13px;
      color: #606266;
      .title {
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 16px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.85);
      }
      .order-num {
        padding-top: 10px;
        white-space: nowrap;
      }
    }
  }
  .list {
    display: flex;
    margin-top: 20px;
    overflow: hidden;
    list-style: none;
    padding: 0;
    .item {
      flex: none;
      width: 200px;
      font-size: 14px;
      line-height: 14px;
      color: rgba(0, 0, 0, 0.85);
      .title {
        margin-bottom: 12px;
        font-size: 13px;
        line-height: 13px;
        color: #666666;
      }
      .value1 {
        color: #f56022;
      }

      .value2 {
        color: #1bbe6b;
      }

      .value3 {
        color: var(--prev-color-primary);
      }

      .value4 {
        color: #6a7b9d;
      }

      .value5 {
        color: #f5222d;
      }
    }
  }
}
.section {
  padding: 25px 0;
  border-bottom: 1px dashed #eeeeee;
  .title {
    padding-left: 10px;
    border-left: 3px solid var(--prev-color-primary);
    font-size: 15px;
    line-height: 15px;
    color: #303133;
  }
  .list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
  }
  .item {
    flex: 0 0 calc(100% / 3);
    display: flex;
    margin-top: 16px;
    font-size: 13px;
    color: #666666;
    &:nth-child(3n + 1) {
      padding-right: 20px;
    }

    &:nth-child(3n + 2) {
      padding-right: 10px;
      padding-left: 10px;
    }

    &:nth-child(3n + 3) {
      padding-left: 20px;
    }
  }
  .value {
    flex: 1;
    image {
      display: inline-block;
      width: 40px;
      height: 40px;
      margin: 0 12px 12px 0;
      vertical-align: middle;
    }
  }
  .item.pic {
    display: flex;
    img {
      width: 80px;
      height: 80px;
    }
  }
}
.tab {
  display: flex;
  align-items: center;
  .el-image {
    width: 36px;
    height: 36px;
    margin-right: 10px;
  }
}
::v-deep .el-drawer__body {
  overflow: auto;
}
.gary {
  color: #aaa;
}
::v-deep .el-drawer__body {
  padding: 20px 0;
}
::v-deep .el-tabs--border-card > .el-tabs__content {
  padding: 0 35px;
}
::v-deep .el-tabs--border-card > .el-tabs__header,
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item:active {
  border: none;
  height: 40px;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  border: none;
  border-top: 2px solid var(--prev-color-primary);
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  line-height: 16px;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  border: none;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item {
  margin-top: 0;
  transition: none;
  height: 40px !important;
  line-height: 40px !important;
  width: 92px !important;
  font-size: 13px;
  font-weight: 400;
  color: #303133;
  line-height: 16px;
}
::v-deep .el-tabs--border-card {
  border: none;
  box-shadow: none;
}

.logistics {
  align-items: center;
  padding: 10px 20px;

  .logistics_img {
    width: 45px;
    height: 45px;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .logistics_cent {
    span {
      display: block;
      font-size: 12px;
    }
  }
}
.trees-coadd {
  width: 100%;
  height: 400px;
  border-radius: 4px;
  overflow: hidden;

  .scollhide {
    width: 100%;
    height: 100%;
    overflow: auto;
    margin-left: 18px;
    padding: 10px 0 10px 0;
    box-sizing: border-box;

    .content {
      font-size: 12px;
    }

    .time {
      font-size: 12px;
      color: #2d8cf0;
    }
  }
}

.logistics-timeline {
  padding: 20px;

  .el-timeline-item {
    padding-bottom: 20px;

    .el-timeline-item__timestamp {
      font-size: 13px;
      color: #909399;
    }

    .el-timeline-item__content {
      p {
        margin: 0;
        line-height: 1.5;
        color: #303133;
      }
    }
  }
}

.trees-coadd {
  .scollhide {
    height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 0 20px;

    .el-timeline-item {
      .time {
        font-size: 13px;
        color: #909399;
        margin-bottom: 5px;
      }

      .content {
        color: #303133;
        line-height: 1.5;
      }
    }
  }
}

.empty-logistics {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>

