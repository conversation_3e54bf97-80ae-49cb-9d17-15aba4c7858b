<template>
  <div>
    <product-list-details ref="productlist"></product-list-details>
  </div>
</template>

<script>
import productListDetails from './orderListDetails';
import { mapMutations } from 'vuex';
export default {
  name: 'list',
  components: {
    productListDetails,
  },
  data() {
    return {
      tabs: [
        {
          type: '',
          label: '全部订单',
          value: Number(this.tablists?.all) || 0,
          max: 999999,
        },
      ],
      spinShow: false,
      currentTab: '',
      data: [],
      tablists: null,
    };
  },
  created() {
    this.getOrderType('');
    this.getOrderStatus('');
    this.getOrderTime('');
    this.getOrderNum('');
    this.getfieldKey('');
    this.getisDelIdListl('');
    this.getIsDel(1);
  },
  beforeDestroy() {
    this.getOrderType('');
    this.getOrderStatus('');
    this.getOrderTime('');
    this.getOrderNum('');
    this.getfieldKey('');
    this.getisDelIdListl('');
    this.getIsDel(1);
  },
  mounted() { },
  methods: {
    ...mapMutations('order', [
      'getOrderStatus',
      'getOrderTime',
      'getOrderNum',
      'getfieldKey',
      'getOrderType',
      'getisDelIdListl',
      'getIsDel',
      // 'onChangeChart'
    ]),
  },
};
</script>
<style lang="scss" scoped>
.product_tabs ::v-deep .ivu-tabs-bar {
  margin-bottom: 0px !important;
}

.product_tabs ::v-deep .ivu-page-header-content {
  margin-bottom: 0px !important;
}

.product_tabs ::v-deep .ivu-page-header-breadcrumb {
  margin-bottom: 0px !important;
}

::v-deep .el-badge__content.is-fixed {
  top: 7px;
}
</style>
