// 导出TableHead 数据
export const GoodsTableHead = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: '80px',
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '划线价',
    slot: 'ot_price',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '商品编码',
    slot: 'bar_code',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '条形码',
    slot: 'bar_code_number',
    align: 'center',
    minWidth: '120px',
  },
  {
    title: '重量（KG）',
    slot: 'weight',
    align: 'center',
    minWidth: '95px',
  },
  {
    title: '体积(m³)',
    slot: 'volume',
    align: 'center',
    minWidth: '95px',
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: '100px',
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: '120px',
  },
];
//   虚拟商品-卡密 优惠券
export const VirtualTableHead = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '原价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '产品编号',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '虚拟商品',
    slot: 'fictitious',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];
//   虚拟商品
export const VirtualTableHead2 = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '原价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '产品编号',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '默认选中规格',
    slot: 'selected_spec',
    fixed: 'right',
    align: 'center',
    minWidth: 90,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];

export const columns2 = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '划线价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '商品编码',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '条形码',
    slot: 'bar_code_number',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '重量（KG）',
    slot: 'weight',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '体积(m³)',
    slot: 'volume',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];

export const columns3 = [
  {
    title: '图片',
    slot: 'pic',
    align: 'center',
    minWidth: 80,
  },
  {
    title: '售价',
    slot: 'price',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '成本价',
    slot: 'cost',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '原价',
    slot: 'ot_price',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '库存',
    slot: 'stock',
    align: 'center',
    minWidth: 95,
  },
  {
    title: '商品编码',
    slot: 'bar_code',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '条形码',
    slot: 'bar_code_number',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '操作',
    slot: 'action',
    fixed: 'right',
    align: 'center',
    minWidth: 120,
  },
];

//自定义留言下拉选择
export const CustomList = [
  {
    value: 'text',
    label: '文本框',
  },
  {
    value: 'number',
    label: '数字',
  },
  {
    value: 'email',
    label: '邮件',
  },
  {
    value: 'data',
    label: '日期',
  },
  {
    value: 'time',
    label: '时间',
  },
  {
    value: 'id',
    label: '身份证',
  },
  {
    value: 'phone',
    label: '手机号',
  },
  {
    value: 'img',
    label: '图片',
  },
];

export const RuleValidate = {
  store_name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  cate_id: [
    {
      required: true,
      message: '请选择商品分类',
      trigger: 'change',
      type: 'array',
      min: '1',
    },
  ],
  unit_name: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  unit_price: [
    { required: true, message: '请输入商品单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '商品单价不能小于0', trigger: 'blur' }
  ],
  slider_image: [
    {
      required: true,
      message: '请上传商品轮播图',
      type: 'array',
      trigger: 'change',
    },
  ],
  spec_type: [{ required: true, message: '请选择商品规格', trigger: 'change' }],
  display_systems: [
    {
      required: true,
      message: '请至少选择一个展示系统',
      type: 'array',
      min: 1,
      trigger: 'change',
    },
  ],
  is_virtual: [{ required: true, message: '请选择商品类型', trigger: 'change' }],
  selectRule: [{ required: true, message: '请选择商品规格属性', trigger: 'change' }],
  temp_id: [
    {
      required: true,
      message: '请选择运费模板',
      trigger: 'change',
      type: 'number',
    },
  ],
  presale_time: [
    {
      required: true,
      type: 'array',
      message: '请选择活动时间',
      trigger: 'change',
    },
  ],
  logistics: [
    {
      required: true,
      type: 'array',
      min: 1,
      message: '请选择物流方式',
      trigger: 'change',
    },
    {
      type: 'array',
      max: 2,
      message: '请选择物流方式',
      trigger: 'change',
    },
  ],
  give_integral: [{ type: 'integer', message: '请输入整数' }],
};
