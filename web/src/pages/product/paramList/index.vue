<template>
  <div>
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form
          ref="artFrom"
          :model="artFrom"
          label-width="80px"
          label-position="right"
          class="tabform"
          @submit.native.prevent
          inline
        >
          <el-form-item label="参数分类：">
            <el-select
              v-model="artFrom.categoryId"
              placeholder="请选择参数分类"
              clearable
              class="form_content_width"
              @change="onCategoryChange"
            >
              <el-option
                v-for="category in categoryList"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="模板搜索：">
            <el-input
              clearable
              v-model="artFrom.name"
              placeholder="请输入模板名称"
              class="form_content_width"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" v-db-click @click="userSearchs">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 分类管理区域 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <div class="category-header">
        <h3>参数分类管理</h3>
        <el-button type="primary" size="small" @click="addCategory">添加分类</el-button>
      </div>
      <el-table
        :data="categoryList"
        v-loading="categoryLoading"
        class="mt14"
        empty-text="暂无分类"
      >
        <el-table-column label="分类ID" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分类名称" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="toggleCategoryStatus(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="参数数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.attributeCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="180">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewCategoryParams(scope.row)">查看参数</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" size="small" @click="editCategory(scope.row)">编辑</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" size="small" @click="deleteCategory(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 参数模板区域 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <div class="param-header">
        <h3>
          属性分类管理
          <span v-if="currentCategory" class="category-info">
            - {{ currentCategory.name }}
          </span>
        </h3>
        <div class="param-actions">
          <el-button type="primary" v-db-click @click="paramAdd">添加属性分类</el-button>
          <el-button
            type="danger"
            :disabled="selectedRows.length === 0"
            @click="batchDelete"
          >
            批量删除 ({{ selectedRows.length }})
          </el-button>
        </div>
      </div>
      <el-table
        ref="table"
        :data="tableList"
        v-loading="loading"
        highlight-current-row
        :row-key="getRowKey"
        @selection-change="handleSelectRow"
        empty-text="暂无数据"
        class="mt14"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="ID" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="属性名称" min-width="130">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属分类" min-width="130">
          <template slot-scope="scope">
            <span>{{ scope.row.categoryName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="属性数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.attributeCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="180">
          <template slot-scope="scope">
            <a v-db-click @click="edit(scope.row)">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a v-db-click @click="manageAttributes(scope.row)">管理属性</a>
            <el-divider direction="vertical"></el-divider>
            <a v-db-click @click="del(scope.row, '删除分类', scope.$index)">删除</a>
          </template>
        </el-table-column>
      </el-table>
      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="artFrom.page"
          :limit.sync="artFrom.limit"
          @pagination="getDataList"
        />
      </div>
    </el-card>

    <!-- 参数添加/编辑弹窗 -->
    <param-add ref="paramAdd" @getList="userSearchs"></param-add>

    <!-- 分类添加/编辑弹窗 -->
    <category-dialog
      :visible.sync="categoryDialogVisible"
      :category="currentEditCategory"
      @success="getCategoryList">
    </category-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import paramAdd from './paramAdd';
import CategoryDialog from './categoryDialog';
import { paramListApi, paramCategoriesApi, deleteParamCategoryApi, paramBatchDeleteApi } from '@/api/product';
export default {
  name: 'paramList',
  components: {
    paramAdd,
    CategoryDialog
  },
  data() {
    return {
      loading: false,
      categoryLoading: false,
      artFrom: {
        page: 1,
        limit: 20,
        name: '',
        categoryId: null,
      },
      tableList: [],
      total: 0,
      selectedIds: new Set(), //选中合并项的id
      ids: [],
      multipleSelection: [],
      selectedRows: [], // 批量选择的行数据
      // 分类相关数据
      categoryList: [],
      currentCategory: null,
      categoryDialogVisible: false,
      currentEditCategory: null,
    };
  },
  computed: {
    ...mapState('admin/order', ['orderChartType']),
  },
  created() {
    this.getCategoryList();
    this.getDataList();
  },
  methods: {
    getRowKey(row) {
      return row.id;
    },
    //全选和取消全选时触发
    handleSelectAll(selection) {
      if (selection.length === 0) {
        //获取table的数据；
        let data = this.$refs.table.data;
        data.forEach((item) => {
          if (this.selectedIds.has(item.id)) {
            this.selectedIds.delete(item.id);
          }
        });
      } else {
        selection.forEach((item) => {
          this.selectedIds.add(item.id);
        });
      }
      this.$nextTick(() => {
        //确保dom加载完毕
        this.setChecked();
      });
    },
    //  选中某一行
    handleSelectRow(selection) {
      const uniqueArr = [];
      const ids = [];
      for (let i = 0; i < selection.length; i++) {
        const item = selection[i];
        if (!ids.includes(item.id)) {
          uniqueArr.push(item);
          ids.push(item.id);
        }
      }
      this.selectedIds = ids;
      this.multipleSelection = uniqueArr;
      this.selectedRows = uniqueArr; // 更新批量选择的行数据
      this.$nextTick((e) => {
        this.setChecked();
      });
    },
    setChecked() {
      //将new Set()转化为数组
      this.ids = [...this.selectedIds].join(',');
    },
    // 删除
    del(row, tit) {
      let delfromData = {
        title: tit,
        num: 0,
        url: `product/param/deleteAttr/${row.id}`,
        method: 'DELETE',
      };
      this.$modalSure(delfromData)
        .then((res) => {
          this.$message.success(res.msg);
          this.getDataList();
        })
        .catch((res) => {
          this.$message.error(res.msg);
        });
    },
    // 批量删除
    batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的参数模板');
        return;
      }

      const selectedNames = this.selectedRows.map(row => row.name).join('、');
      this.$confirm(
        `确定要删除以下参数模板吗？\n${selectedNames}`,
        '批量删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        const ids = this.selectedRows.map(row => row.id);
        this.performBatchDelete(ids);
      }).catch(() => {
        // 用户取消删除
      });
    },
    // 执行批量删除
    performBatchDelete(ids) {
      this.loading = true;

      paramBatchDeleteApi(ids)
        .then((res) => {
          this.$message.success(`成功删除 ${ids.length} 个参数分类`);
          this.getDataList();
          // 清空选择
          this.selectedRows = [];
          this.selectedIds = new Set();
          this.multipleSelection = [];
          this.$refs.table.clearSelection();
        })
        .catch((res) => {
          this.$message.error(res.msg || '批量删除失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    paramAdd() {
      this.$refs.paramAdd.modal = true;
    },
    // 编辑
    edit(row) {
      this.$refs.paramAdd.modal = true;
      this.$refs.paramAdd.getIofo(row);
    },
    // 管理属性
    manageAttributes(row) {
      // 跳转到属性管理页面，传递分类ID
      this.$router.push({
        path: '/product/attribute',
        query: { categoryId: row.id, categoryName: row.name }
      });
    },
    // 列表；
    getDataList() {
      this.loading = true;
      paramListApi(this.artFrom)
        .then((res) => {
          let data = res.data;
          this.tableList = data.list || [];
          this.$nextTick(() => {
            //确保dom加载完毕
            this.setChecked();
          });
          this.total = res.data.count || 0;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg);
        });
    },
    // 表格搜索
    userSearchs() {
      this.artFrom.page = 1;
      this.getDataList();
    },

    // 获取分类列表
    getCategoryList() {
      this.categoryLoading = true;
      paramCategoriesApi()
        .then((res) => {
          this.categoryList = res.data || [];
          this.categoryLoading = false;
        })
        .catch((res) => {
          this.categoryLoading = false;
          this.$message.error(res.msg || '获取分类列表失败');
        });
    },

    // 分类变化
    onCategoryChange(categoryId) {
      this.currentCategory = this.categoryList.find(cat => cat.id === categoryId) || null;
      this.userSearchs();
    },

    // 查看分类下的参数
    viewCategoryParams(category) {
      this.artFrom.categoryId = category.id;
      this.currentCategory = category;
      this.userSearchs();
    },

    // 添加分类
    addCategory() {
      this.currentEditCategory = null;
      this.categoryDialogVisible = true;
    },

    // 编辑分类
    editCategory(category) {
      this.currentEditCategory = { ...category };
      this.categoryDialogVisible = true;
    },

    // 删除分类
    deleteCategory(category) {
      this.$confirm(`确定要删除分类"${category.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteParamCategoryApi(category.id)
          .then((res) => {
            this.$message.success(res.msg || '删除成功');
            this.getCategoryList();
            // 如果当前选中的分类被删除，清空选择
            if (this.artFrom.categoryId === category.id) {
              this.artFrom.categoryId = null;
              this.currentCategory = null;
              this.userSearchs();
            }
          })
          .catch((res) => {
            this.$message.error(res.msg || '删除失败');
          });
      }).catch(() => {
        // 取消删除
      });
    },

    // 切换分类状态
    toggleCategoryStatus(category) {
      // TODO: 调用更新分类状态API
      this.$message.success(`${category.enabled ? '启用' : '禁用'}成功`);
    },
  },
};
</script>

<style scoped>
.category-header, .param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-header h3, .param-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.category-info {
  color: #409EFF;
  font-weight: normal;
}

.param-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.param-actions .el-button {
  margin-left: 0;
}

.mt16 {
  margin-top: 16px;
}
</style>
