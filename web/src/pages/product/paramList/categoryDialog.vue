<template>
  <el-dialog
    :title="isEdit ? '编辑分类' : '添加分类'"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="enabled">
        <el-switch
          v-model="form.enabled"
          active-text="启用"
          inactive-text="禁用"
        ></el-switch>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addParamCategoryApi, editParamCategoryApi } from '@/api/product';

export default {
  name: 'CategoryDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    category: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      form: {
        name: '',
        enabled: true
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    isEdit() {
      return this.category && this.category.id;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm();
      }
    }
  },
  methods: {
    initForm() {
      if (this.category) {
        this.form = {
          name: this.category.name || '',
          enabled: this.category.enabled !== false
        };
      } else {
        this.form = {
          name: '',
          enabled: true
        };
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;

          const apiCall = this.isEdit
            ? editParamCategoryApi(this.category.id, this.form)
            : addParamCategoryApi(this.form);

          apiCall
            .then((res) => {
              this.loading = false;
              this.$message.success(res.msg || (this.isEdit ? '编辑成功' : '添加成功'));
              this.$emit('success');
              this.handleClose();
            })
            .catch((res) => {
              this.loading = false;
              this.$message.error(res.msg || (this.isEdit ? '编辑失败' : '添加失败'));
            });
        }
      });
    },

    handleClose() {
      this.dialogVisible = false;
      this.form = {
        name: '',
        enabled: true
      };
    }
  }
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
