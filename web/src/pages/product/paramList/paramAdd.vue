<template>
  <el-dialog :visible.sync="modal" @closed="onCancel" title="属性分类" width="600px" v-loading="spinShow">
    <el-form
        ref="formDynamic"
        :model="formDynamic"
        class="attrFrom"
        label-position="right"
        label-width="auto"
        @submit.native.prevent
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="分类名称：" prop="name">
            <el-select
              v-model="formDynamic.categoryId"
              placeholder="请选择或输入分类名称"
              filterable
              allow-create
              default-first-option
              clearable
            >
              <el-option
                v-for="category in categoryList"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态：" prop="enabled">
            <el-radio-group v-model="formDynamic.enabled">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="noForm" :key="index">
          <el-form-item label="">
            <div class="specifications">
              <el-table ref="selection" :data="formDynamic.value">
                <el-table-column width="50">
                  <template slot-scope="scope">
                    <div class="drag" @on-drag-drop="onDragDrop">
                      <img class="handle" src="@/assets/images/drag-icon.png" alt="" />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="参数名称" min-width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="参数值" min-width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value"></el-input>
                  </template>
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="80">
                  <template slot-scope="scope">
                    <a class="submission mr15" v-db-click @click="deleteRow(scope.$index)">删除</a>
                  </template>
                </el-table-column>
              </el-table>
              <el-button
                  v-if="formDynamic.value.length < 8"
                  type="primary"
                  class="submission mr15 mt20"
                  v-db-click
                  @click="handleAddRole"
              >添加参数</el-button
              >
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button v-db-click @click="onClose">取消</el-button>
      <el-button type="primary" :loading="modal_loading" v-db-click @click="handleSubmit('formDynamic')"
      >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex';
import {paramSaveApi, paramInfoApi, paramCategoriesApi} from '@/api/product';
import vuedraggable from 'vuedraggable';
import Sortable from 'sortablejs';
export default {
  name: 'addAttr',
  components: {
    draggable: vuedraggable,
  },
  data() {
    return {
      spinShow: false,
      modal_loading: false,
      grid: {
        xl: 3,
        lg: 3,
        md: 12,
        sm: 24,
        xs: 24,
      },
      modal: false,
      index: 1,
      formDynamic: {
        id: 0,
        categoryId: '',
        name: '',
        sort: 0,
        value: [],
      },
      attrsName: '',
      attrsVal: '',
      formDynamicNameData: [],
      isBtn: false,
      formDynamicName: [],
      results: [],
      result: [],
      ids: 0,
    };
  },
  watch: {
    modal(val) {
      if (val) {
        this.$nextTick(() => {
          this.setSort();
        });
      }
    },
  },
  mounted() {
    this.getCategoryList();
  },
  methods: {
    setSort() {
      // ref一定跟table上面的ref一致
      const el = this.$refs.selection.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0];
      this.sortable = Sortable.create(el, {
        ghostClass: 'sortable-ghost',
        handle: '.handle',
        setData: function (dataTransfer) {
          dataTransfer.setData('Text', '');
        },
        // 监听拖拽事件结束时触发
        onEnd: (evt) => {
          this.elChangeExForArray(evt.oldIndex, evt.newIndex, this.formDynamic.value);
        },
      });
    },
    elChangeExForArray(index1, index2, array, init) {
      const arr = array;
      const temp = array[index1];
      const tempt = array[index2];
      if (init) {
        arr[index2] = tempt;
        arr[index1] = temp;
      } else {
        arr[index1] = tempt;
        arr[index2] = temp;
      }
      this.formDynamic.value = [];
      this.$nextTick((e) => {
        this.formDynamic.value = arr;
      });
    },
    handleShowPop(index) {
      this.$refs['inputRef_' + index][0].focus();
    },
    handleAddRole() {
      let data = {
        name: '',
        value: '',
      };
      this.formDynamic.value.push(data);
    },
    onCancel() {
      this.ids = 0;
      this.clear();
    },
    onClose() {
      this.ids = 0;
      this.clear();
      this.modal = false;
    },
    deleteRow(index) {
      this.formDynamic.value.splice(index, 1);
    },
    // 添加按钮
    addBtn() {
      this.isBtn = true;
    },
    //修改排序
    onDragDrop(a, b) {
      console.log(a, b);
      this.formDynamic.value.splice(b, 1, ...this.formDynamic.value.splice(a, 1, this.formDynamic.value[b]));
    },
    // 详情
    getIofo(row) {
      this.ids = row.id;
      paramInfoApi(row.id)
          .then((res) => {
            this.formDynamic = res.data;
            // 如果有分类ID，加载对应的属性列表
            if (this.formDynamic.categoryId) {
              this.getAttributesByCategory(this.formDynamic.categoryId);
            }
          })
          .catch((res) => {
            this.spinShow = false;
            this.$message.error(res.msg);
          });
    },
    // 提交
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.formDynamic.value.length === 0) {
            return this.$message.warning('请至少添加一条商品参数！');
          }
          this.modal_loading = true;
          paramSaveApi(this.formDynamic)
              .then((res) => {
                this.$message.success(res.msg);
                setTimeout(() => {
                  this.modal = false;
                  this.modal_loading = false;
                }, 500);
                setTimeout(() => {
                  this.$emit('getList');
                  this.clear();
                }, 600);
              })
              .catch((res) => {
                this.modal_loading = false;
                this.$message.error(res.msg);
              });
        } else {
          return false;
        }
      });
    },
    clear() {
      this.$refs['formDynamic'].resetFields();
      this.formDynamic.value = [];
      this.formDynamic.categoryId = '';
      this.formDynamic.name = '';
      this.formDynamic.sort = '';
      this.isBtn = false;
      this.attrsName = '';
      this.attrsVal = '';
      this.ids = 0;
      this.attributeList = [];
    },
    // 删除
    handleRemove(index) {
      this.formDynamic.value.splice(index, 1);
    },
    // 获取分类列表
    getCategoryList() {
      console.log('获取分类列表开始');
      paramCategoriesApi()
        .then((res) => {
          console.log('分类列表API响应:', res);
          this.categoryList = res.data || [];
          console.log('设置的分类列表:', this.categoryList);
          if (this.categoryList.length === 0) {
            this.$message.warning('暂无参数分类数据，请先在分类管理中添加分类');
          }
        })
        .catch((res) => {
          console.error('获取分类列表失败:', res);
          this.$message.error(res.msg || '获取分类列表失败');
        });
    },
    // 根据分类获取属性
    getAttributesByCategory(categoryId) {
      if (!categoryId) {
        this.attributeList = [];
        return;
      }

      paramListApi({ categoryId: categoryId, page: 1, limit: 1000 })
        .then((res) => {
          this.attributeList = res.data.list || [];
        })
        .catch((res) => {
          this.$message.error(res.msg || '获取属性列表失败');
        });
    },
    // 分类选择变化处理
    onCategoryChange(categoryId) {
      this.formDynamic.categoryId = categoryId;
      // 根据选择的分类获取对应的属性
      this.getAttributesByCategory(categoryId);
      // 清空已选择的参数
      this.formDynamic.value = [];
      // 设置模板名称为分类名称
      const selectedCategory = this.categoryList.find(cat => cat.id === categoryId);
      if (selectedCategory) {
        this.formDynamic.name = selectedCategory.name;
      }
    },
    // 属性选择变化处理
    onAttributeChange(row, index) {
      const selectedAttr = this.attributeList.find(attr => attr.id === row.attributeId);
      if (selectedAttr) {
        row.name = selectedAttr.name;
        // 如果属性有默认值，可以设置到value中
        if (selectedAttr.values && selectedAttr.values.length > 0) {
          row.value = selectedAttr.values[0];
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.rulesBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.attrFrom {
  ::v-deep .ivu-form-item {
    margin-bottom: 0px !important;
  }
}
.noForm {
  margin-left: 12px;
}
.add {
  margin-left: 132px;
}
.drag {
  cursor: move;
}
.value {
  display: block;
  margin: 5px 0;
  position: relative;
  .el-icon-error {
    position: absolute;
    display: none;
    right: -3px;
    top: -3px;
    z-index: 9;
  }
}
.value:hover {
  .el-icon-error {
    display: block;
    z-index: 999;
    cursor: pointer;
  }
}
.move-icon {
  width: 30px;
  cursor: move;
  margin-right: 10px;
}
.move-icon .icondrag2 {
  font-size: 26px;
  color: #bbb;
}
.specifications {
  .specifications-item:hover {
    background-color: var(--prev-bg-menu-hover-ba-color);
  }
  .specifications-item:hover .del {
    display: block;
  }
  .specifications-item:last-child {
    margin-bottom: 14px;
  }
  .specifications-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px 15px;
    transition: all 0.1s;
    background-color: #fafafa;
    margin-bottom: 10px;
    border-radius: 4px;
    .del {
      display: none;
      position: absolute;
      right: 15px;
      top: 15px;
      font-size: 22px;
      color: var(--prev-color-primary);
      cursor: pointer;
    }
    .specifications-item-box {
      position: relative;
      .lineBox {
        position: absolute;
        left: 13px;
        top: 24px;
        width: 30px;
        height: 45px;
        border-radius: 6px;
        border-left: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
      }
      .specifications-item-name-input {
        width: 200px;
      }
    }
  }
  .rulesBox {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .item {
      display: flex;
      flex-wrap: wrap;
    }
    .addfont {
      margin-top: 5px;
    }
    ::v-deep .el-popover {
      border: none;
      box-shadow: none;
      padding: 0;
      margin-top: 5px;
      line-height: 1.5;
    }
  }
  .addfont {
    display: inline-block;
    font-size: 12px;
    font-weight: 400;
    color: var(--prev-color-primary);
    margin-left: 14px;
    cursor: pointer;
  }
}
</style>
