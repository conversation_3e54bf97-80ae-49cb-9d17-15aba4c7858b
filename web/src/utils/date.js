/**
 * 日期格式化工具
 */

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date|string|number} date 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date) {
    return '-';
  }
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '-';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm:ss
 * @param {Date|string|number} date 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date) {
  if (!date) {
    return '-';
  }
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '-';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string|number} startDate 开始日期
 * @param {Date|string|number} endDate 结束日期
 * @returns {number} 天数差
 */
export function daysBetween(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // 重置时间部分，只比较日期
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);
  
  // 计算天数差
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

/**
 * 获取相对于指定日期的日期
 * @param {Date|string|number} date 基准日期
 * @param {number} days 相对天数，正数为之后，负数为之前
 * @returns {Date} 计算后的日期
 */
export function getRelativeDate(date, days) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

/**
 * 检查日期是否在指定范围内
 * @param {Date|string|number} date 要检查的日期
 * @param {Date|string|number} startDate 范围开始日期
 * @param {Date|string|number} endDate 范围结束日期
 * @returns {boolean} 是否在范围内
 */
export function isDateInRange(date, startDate, endDate) {
  const d = new Date(date).getTime();
  const start = new Date(startDate).getTime();
  const end = new Date(endDate).getTime();
  
  return d >= start && d <= end;
} 