/**
 * Format image URL to ensure it loads correctly
 * @param {string} url - The image URL to format
 * @returns {string} - The formatted URL
 */
export function formatImageUrl(url) {
  if (!url) return '';

  // Handle blob URLs (for file previews)
  if (url.startsWith('blob:')) {
    return url;
  }

  // If URL already starts with http or https, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // Handle weshop-wjhx uploads
  if (url.includes('weshop-wjhx/uploads')) {
    return 'http://localhost:9999' + url;
  }

  // Handle uploads path
  if (url.includes('/uploads/')) {
    return 'http://localhost:9999/weshop-wjhx' + url;
  }

  // For all other relative URLs, prepend the host
  const host = window.location.protocol + '//' + window.location.host;
  return host + (url.startsWith('/') ? url : '/' + url);
}
